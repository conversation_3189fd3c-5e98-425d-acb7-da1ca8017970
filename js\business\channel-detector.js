/**
 * ============================================================================
 * 渠道检测器模块 (channel-detector.js) - 防AI幻觉核心声明
 * ============================================================================
 *
 * 【防AI幻觉声明】
 * 本文件是渠道检测和模式匹配的统一管理中心，负责从文本中识别和分类渠道信息。
 * 严禁AI基于文件名推测功能，必须完整阅读所有检测规则、正则表达式和匹配算法才能理解渠道检测机制的完整性。
 * 本文件不直接处理业务逻辑，仅提供渠道检测和模式匹配服务。
 *
 * 【重复开发防护】
 * 警告：本项目已存在完整的渠道检测系统，严禁创建新的渠道检测模块。
 * 任何新的渠道检测需求必须通过本工具实现，而不是创建分散的检测逻辑。
 * 现有渠道检测模式：[快速路径检测 → 参考号检测 → 关键词检测 → 通用模式匹配] - 严禁修改此约定。
 *
 * 【竞态问题防护】
 * 渠道检测竞态防护：
 * 1. 检测流程序列化：detectChannel方法中的各检测步骤按顺序执行
 * 2. 规则处理同步：processDetectionRules方法中的规则转换同步执行
 * 3. 正则表达式编译：patternToRegex方法中的正则编译原子化
 * 4. 规则更新同步：updateDetectionRules方法中的规则更新按顺序执行
 * 防护措施：使用同步方法调用、原子性规则处理、顺序检测流程确保检测的可靠性。
 *
 * 【声明与接口】
 * 导出：ChannelDetector类、createChannelDetectorModule工厂函数
 * 导入：依赖注入的configManager（配置管理器）
 * 主要接口：
 *   - detectChannel(input)：主要渠道检测接口
 *   - processDetectionRules()：处理和转换检测规则
 *   - detectFliggy(text)：检测Fliggy渠道
 *   - detectJingGe(text)：检测JingGe渠道
 *   - detectByReference(text)：通过参考号检测渠道
 *   - detectByKeywords(text)：通过关键词检测渠道
 *   - getDetectionRules()：获取当前检测规则
 *   - updateDetectionRules(newRules)：更新检测规则
 *   - addChannelRule(channelName, patterns, confidence)：添加新的渠道规则
 *
 * 【依赖关系网络】
 * 直接依赖：
 *   - configManager：配置管理器用于获取检测规则和数据工具 @INJECTED
 *   - DataUtils：数据工具类用于正则表达式处理 @VIA_CONFIG
 *
 * 被依赖关系：
 *   - 被main.js注册为渠道检测模块
 *   - 被field-mapper.js依赖用于渠道检测和提示词选择
 *   - 被所有需要渠道识别的业务模块间接依赖
 *   - 影响整个应用的渠道分类和数据处理流程
 *
 * 【加载时机与生命周期】
 * 加载时机：模块容器初始化阶段按依赖顺序加载
 * 初始化时机：构造函数中接收configManager依赖并处理检测规则
 * 生命周期：
 *   1. 文件加载 → 2. 工厂函数注册 → 3. 依赖解析 → 4. 实例创建 → 5. 检测规则处理 → 6. 运行时渠道检测 → 7. 规则匹配执行 → 8. 结果评估返回 → 9. 规则更新处理 → 10. 实例销毁
 * 清理处理：无特殊清理需求（规则存储在内存中）
 *
 * 【技术栈与环境要求】
 * 运行环境：浏览器环境（依赖ES6+和正则表达式）
 * 技术栈：原生JavaScript ES6+，依赖注入模式
 * 浏览器支持：现代浏览器（支持RegExp、Map、Object.entries）
 * 数据处理：支持正则表达式匹配、字符串处理、模式识别
 *
 * 【核心功能说明】
 * 1. 多层检测：支持快速路径、参考号、关键词和通用模式检测
 * 2. 规则管理：动态规则处理和更新机制
 * 3. 置信度评估：所有检测结果都有置信度评分
 * 4. 模式匹配：使用正则表达式进行精确模式匹配
 * 5. 错误处理：完整的异常处理和降级机制
 * 6. 规则扩展：支持动态添加新的渠道检测规则
 * 7. 性能优化：检测顺序优化和快速路径机制
 *
 * 【相关文件索引】
 * - main.js：负责本模块的注册和依赖注入
 * - field-mapper.js：使用本模块进行渠道检测
 * - config.js：提供配置管理和检测规则
 * - prompt-fragment-manager.js：使用渠道信息选择提示词
 *
 * 【修改记录】
 * v2.0.0 (2025-08-31) - 模块化重构版本
 *   - 消除全局变量依赖，采用依赖注入模式
 *   - 重构检测流程，提高检测准确性和性能
 *   - 添加详细的防AI幻觉注释系统
 *   - 实现规则动态管理和扩展机制
 *   - 优化检测算法和置信度评估
 *
 * 【使用说明】
 * 渠道检测：使用detectChannel方法进行智能渠道识别
 * 规则管理：使用getDetectionRules获取当前规则配置
 * 规则更新：使用updateDetectionRules动态更新检测规则
 * 规则添加：使用addChannelRule添加新的渠道检测规则
 * 专项检测：使用detectFliggy或detectJingGe进行特定渠道检测
 * 辅助检测：使用detectByReference或detectByKeywords进行辅助检测
 *
 * ============================================================================
 */

/**
 * 渠道检测器模块 - Refactored
 *
 * 设计原则：
 * - 依赖注入：通过构造函数接收ConfigManager。
 * - 单一职责：专注于渠道检测逻辑。
 * - 可测试性：支持模拟规则配置的单元测试。
 */
class ChannelDetector {
    constructor(configManager) {
        if (!configManager) {
            throw new Error("ConfigManager is a required dependency for ChannelDetector.");
        }
        this.configManager = configManager;
        this.dataUtils = this.configManager.DataUtils;
        this.detectionRules = this.processDetectionRules();
        console.log('渠道检测器已初始化 - Refactored');
    }

    processDetectionRules() {
        const config = {
            channelRules: this.configManager.getChannelDetectionRules(),
            referencePatterns: this.configManager.getReferencePatterns(),
            keywordDetection: this.configManager.getKeywordDetection()
        };

        const rules = {};

        if (config.channelRules) {
            for (const [key, rule] of Object.entries(config.channelRules)) {
                const patterns = Array.isArray(rule.patterns) ? rule.patterns : [];
                rules[key] = {
                    patterns: patterns.map(p => {
                        try { return this.dataUtils.patternToRegex(p instanceof RegExp ? p.source : String(p || '')); } catch { return null; }
                    }).filter(Boolean),
                    confidence: typeof rule.confidence === 'number' ? rule.confidence : 0.8,
                    channel: rule.channel || rule.name || key
                };
            }
        }

        rules.referencePatterns = config.referencePatterns || {};
        rules.keywordPatterns = config.keywordDetection || {};

        return rules;
    }

    detectChannel(input) {
        try {
            if (!input || typeof input !== 'string') {
                return { channel: null, confidence: 0, method: 'invalid_input' };
            }

            // 1) 特定渠道快速路径
            const fliggyResult = this.detectFliggy(input);
            if (fliggyResult.confidence > 0.8) return fliggyResult;

            const jinggeResult = this.detectJingGe(input);
            if (jinggeResult.confidence > 0.8) return jinggeResult;

            // 2) 通用参考号/关键词
            const referenceResult = this.detectByReference(input);
            if (referenceResult.confidence > 0.8) return referenceResult;

            const keywordResult = this.detectByKeywords(input);
            if (keywordResult.confidence > 0.6) return keywordResult;

            // 3) 通用通道遍历匹配（最小化新增）
            let best = { channel: null, confidence: 0, method: 'generic_no_match' };
            for (const [key, rule] of Object.entries(this.detectionRules)) {
                if (!rule || key === 'referencePatterns' || key === 'keywordPatterns') continue;
                const patterns = Array.isArray(rule.patterns) ? rule.patterns : [];
                for (const pattern of patterns) {
                    try {
                        if (pattern && pattern.test && pattern.test(input)) {
                            const conf = typeof rule.confidence === 'number' ? rule.confidence : 0.8;
                            if (conf > best.confidence) {
                                best = {
                                    channel: rule.channel || key,
                                    confidence: conf,
                                    method: 'generic_pattern',
                                    matchedPattern: pattern.source || String(pattern)
                                };
                            }
                        }
                    } catch {}
                }
            }
            if (best.confidence > 0) return best;

            return { channel: null, confidence: 0, method: 'no_match' };

        } catch (error) {
            console.error('渠道检测失败:', error);
            return { channel: null, confidence: 0, method: 'error', error: error.message };
        }
    }

    detectFliggy(text) {
        const rules = this.detectionRules.fliggy;
        if (!rules) return { channel: null, confidence: 0 };
        let maxConfidence = 0;
        let matchedPattern = null;

        for (const pattern of rules.patterns) {
            const matches = text.match(pattern);
            if (matches && matches.length > 0) {
                if (pattern.source.includes('订单编号')) {
                    maxConfidence = 0.95;
                    matchedPattern = '订单编号+19位数字';
                    break;
                } else {
                    maxConfidence = Math.max(maxConfidence, 0.85);
                    matchedPattern = pattern.source;
                }
            }
        }

        return {
            channel: maxConfidence > 0 ? rules.channel : null,
            confidence: maxConfidence,
            method: 'fliggy_pattern',
            matchedPattern
        };
    }

    detectJingGe(text) {
        const rules = this.detectionRules.jingge;
        if (!rules) return { channel: null, confidence: 0 };
        let maxConfidence = 0;
        let matchedPattern = null;

        for (const pattern of rules.patterns) {
            const matches = text.match(pattern);
            if (matches && matches.length > 0) {
                maxConfidence = rules.confidence;
                matchedPattern = pattern.source;
                break;
            }
        }

        return {
            channel: maxConfidence > 0 ? rules.channel : null,
            confidence: maxConfidence,
            method: 'jingge_pattern',
            matchedPattern
        };
    }

    detectByReference(text) {
        const referencePattern = /\b([A-Z]{2})[A-Z0-9]{6,12}\b/g;
        const matches = text.match(referencePattern);

        if (matches && matches.length > 0) {
            for (const match of matches) {
                const prefix = match.substring(0, 2);
                const rule = this.detectionRules.referencePatterns[prefix];
                
                if (rule) {
                    return {
                        channel: rule.channel,
                        confidence: rule.confidence,
                        method: 'reference_pattern',
                        matchedReference: match
                    };
                }
            }
        }

        return { channel: null, confidence: 0, method: 'reference_no_match' };
    }

    detectByKeywords(text) {
        const lowerText = text.toLowerCase();

        for (const [keyword, rule] of Object.entries(this.detectionRules.keywordPatterns || {})) {
            if (lowerText.includes(keyword.toLowerCase())) {
                return {
                    channel: rule.channel,
                    confidence: rule.confidence,
                    method: 'keyword_match',
                    matchedKeyword: keyword
                };
            }
        }

        return { channel: null, confidence: 0, method: 'keyword_no_match' };
    }

    getDetectionRules() {
        const clone = {};
        for (const [key, rule] of Object.entries(this.detectionRules)) {
            if (key === 'referencePatterns' || key === 'keywordPatterns') {
                clone[key] = rule;
                continue;
            }
            if (!rule || !Array.isArray(rule.patterns)) continue;
            clone[key] = {
                channel: rule.channel,
                confidence: rule.confidence,
                patterns: rule.patterns.map(p => p instanceof RegExp ? p.source : (typeof p === 'string' ? p : ''))
            };
        }
        return clone;
    }

    updateDetectionRules(newRules) {
        for (const [key, rule] of Object.entries(newRules)) {
            if (key === 'referencePatterns' || key === 'keywordPatterns') {
                this.detectionRules[key] = rule;
                continue;
            }
            if (!rule) continue;
            const patterns = Array.isArray(rule.patterns) ? rule.patterns : [];
            this.detectionRules[key] = {
                channel: rule.channel || this.detectionRules[key]?.channel || key,
                confidence: typeof rule.confidence === 'number' ? rule.confidence : (this.detectionRules[key]?.confidence || 0.8),
                patterns: patterns
                    .map(p => {
                        try {
                            if (p instanceof RegExp) return p;
                            if (typeof p === 'string') return this.dataUtils.patternToRegex(p);
                            return null;
                        } catch { return null; }
                    })
                    .filter(Boolean)
            };
        }
        console.log('检测规则已更新', this.detectionRules);
        return true;
    }

    addChannelRule(channelName, patterns, confidence = 0.8) {
        this.detectionRules[channelName.toLowerCase()] = {
            patterns: patterns.map(p => new RegExp(p)),
            confidence: confidence,
            channel: channelName
        };
        console.log(`已添加渠道规则: ${channelName}`);
        return true;
    }
}

// 模块工厂函数
function createChannelDetectorModule(container) {
    const configManager = container.get('config');
    return new ChannelDetector(configManager);
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('channelDetector', createChannelDetectorModule, ['config']);
    console.log('📦 ChannelDetector已注册到模块容器');
}