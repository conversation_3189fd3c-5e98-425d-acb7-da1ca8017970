/**
 * ============================================================================
 * 应用程序主入口文件 (main.js) - 防AI幻觉核心声明
 * ============================================================================
 *
 * 【防AI幻觉声明】
 * 本文件是整个应用的唯一启动入口，负责协调所有模块的加载和初始化。
 * 严禁AI基于文件名推测功能，必须完整阅读本文件内容后才能理解其职责。
 * 本文件不包含任何业务逻辑，仅负责模块编排和生命周期管理。
 *
 * 【重复开发防护】
 * 警告：本项目已存在完整的模块化架构，严禁创建新的应用入口文件。
 * 任何新的初始化逻辑必须在本文件中注册为模块，而不是创建平行入口。
 * 现有模块注册模式：[模块名, 创建函数, 依赖数组] - 严禁修改此约定。
 *
 * 【竞态问题防护】
 * 异步初始化序列严格按照数字阶段执行（阶段1→5），每个阶段都等待前一阶段完成。
 * DOM加载事件与模块初始化异步操作存在竞态：DOM加载完成后立即开始模块注册，
 * 但模块初始化是异步的，可能在DOM完全渲染后才完成。
 * 防护措施：使用async/await确保初始化顺序，使用try/catch捕获异步错误。
 *
 * 【声明与接口】
 * 导出：无（立即执行函数）
 * 导入：无（依赖全局window对象）
 * 全局接口：依赖window.moduleContainer（必须在HTML中预先加载）
 * 事件接口：监听'document.DOMContentLoaded'事件
 *
 * 【依赖关系网络】
 * 直接依赖：
 *   - window.moduleContainer：模块容器，全局依赖 @CRITICAL
 *   - 所有createXXXModule函数：模块创建函数，由HTML文件提供 @REQUIRED
 *
 * 被依赖关系：
 *   - 被index.html直接引用和执行
 *   - 所有业务模块都间接依赖本文件的初始化顺序
 *   - 影响整个应用的启动成功率
 *
 * 【加载时机与生命周期】
 * 加载时机：HTML解析阶段同步加载
 * 执行时机：DOM完全加载后异步执行
 * 生命周期：
 *   1. 文件加载 → 2. DOM加载事件触发 → 3. 模块注册阶段 → 4. 容器初始化 → 5. 应用就绪
 * 失败处理：捕获初始化异常并显示错误页面
 *
 * 【技术栈与环境要求】
 * 运行环境：浏览器环境（依赖window对象和DOM API）
 * 技术栈：原生JavaScript ES6+，无框架依赖
 * 浏览器支持：现代浏览器（支持async/await和模块化）
 *
 * 【核心功能说明】
 * 1. 统一模块注册：按依赖顺序注册所有应用模块
 * 2. 异步初始化管理：协调异步模块初始化过程
 * 3. 错误边界处理：捕获初始化失败并提供用户友好的错误提示
 * 4. 启动状态管理：提供详细的启动日志和状态反馈
 *
 * 【相关文件索引】
 * - index.html：HTML入口文件，提供模块创建函数
 * - module-container.js：模块容器实现
 * - config.js：配置模块
 * - app.js：主应用模块
 * - 其他所有模块文件：业务功能模块
 *
 * 【修改记录】
 * v1.0.0 (2025-08-31) - 初始版本重构
 *   - 重构模块注册逻辑为阶段化加载
 *   - 添加详细的初始化日志
 *   - 完善错误处理机制
 *   - 添加防AI幻觉注释系统
 *
 * 【使用说明】
 * 本文件会在DOM加载完成后自动执行，无需手动调用。
 * 如果初始化失败，请检查：
 * 1. module-container.js是否正确加载
 * 2. 所有createXXXModule函数是否在HTML中定义
 * 3. 浏览器控制台是否有错误信息
 *
 * ============================================================================
 */

/**
 * 【模块注册架构混合问题记录 - 2025-01-01】
 * 
 * 当前架构模式：
 * 1. 集中式注册：在此文件中注册核心模块（config, fieldMapper等）
 * 2. 工厂函数模式：模块通过window.createXXXModule工厂函数创建
 * 3. 依赖管理：通过container.register的第三个参数声明依赖
 * 
 * 问题：
 * - 新增的协同组件（promptProcessor, promptManageModal等）使用自注册模式
 * - 造成注册时机不同步，依赖解析失败
 * - 部分模块不在此文件的依赖图中管理
 * 
 * 缺失的模块注册：
 * - promptProcessor (需要依赖: fieldMapper, promptComposer, config, channelDetector)
 * - promptManageModal (需要依赖: channelDataManager, promptComposer, localStorageManager, promptFragmentManager)
 * - mainModalCoordinator (无依赖)
 * 
 * 模块注册模式对比：
 * 1. 标准工厂模式：config.js使用window.createConfigModule = function()
 * 2. 内联工厂模式：field-mapper.js使用function createFieldMapperModule()
 * 3. 直接自注册模式：prompt-processor.js使用moduleContainer.register()
 * 
 * 时机问题：
 * - 自注册在文件加载时立即执行（同步）
 * - 集中注册在DOMContentLoaded时执行（异步）
 * - 导致容器初始化时找不到依赖模块
 * 
 * 建议解决方案：
 * 1. 短期：在此文件中添加缺失的模块注册（兼容性方案）
 * 2. 长期：统一为单一注册模式（工厂模式或自注册模式）
 * 
 * ============================================================================
 * 
 * 应用程序主入口 (Refactored)
 *
 * 职责:
 * 1. 注册所有应用模块到模块容器。
 * 2. 定义模块加载顺序和依赖关系。
 * 3. 初始化模块容器并启动应用。
 */
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 DOM已加载，开始统一初始化流程...');

    const container = window.moduleContainer;
    if (!container) {
        console.error('❌ 模块容器未找到！初始化失败。');
        return;
    }

    try {
        // 阶段 1: 注册核心服务和配置
        container.register('config', createConfigModule, []);
        container.register('localStorageManager', createLocalStorageManagerModule, []);
        container.register('gemini', createGeminiModule, ['localStorageManager']);
        
        // 阶段 2: 注册核心业务逻辑模块
        container.register('channelDetector', createChannelDetectorModule, ['config']);
        container.register('addressTranslator', createAddressTranslatorModule, ['config']);
        container.register('channelDataManager', createChannelDataManagerModule, ['localStorageManager']);
        container.register('promptFragmentManager', createPromptFragmentManagerModule, []);
        container.register('promptComposer', createPromptComposerModule, ['promptFragmentManager']);

        // 阶段 3: 注册依赖于业务逻辑的上层模块
        container.register('fieldMapper', createFieldMapperModule, ['gemini', 'channelDetector', 'addressTranslator', 'promptFragmentManager']);
        
        // 阶段 4: 注册UI和编辑器模块
        container.register('ruleEditor', createRuleEditorModule, ['config', 'channelDetector', 'localStorageManager', 'channelDataManager']);
        container.register('promptEditor', createPromptEditorModule, ['config', 'fieldMapper', 'promptComposer', 'localStorageManager', 'gemini', 'channelDataManager']);

        // 阶段 5: 注册协同组件（新增 - 阶段2统一架构）
        container.register('promptProcessor', createPromptProcessorModule, 
            ['fieldMapper', 'promptComposer', 'config', 'channelDetector']);
        
        container.register('mainModalCoordinator', createMainModalCoordinatorModule, []);
        
        container.register('promptManageModal', createPromptManageModalModule, 
            ['channelDataManager', 'promptComposer', 'localStorageManager', 'promptFragmentManager']);

        // 阶段 6: 注册主应用模块
        container.register('app', createApplicationModule, ['fieldMapper', 'channelDetector']);

        // 最终初始化
        console.log('⚙️ 所有模块注册完毕（包含协同组件），开始初始化容器...');
        await container.initialize();
        await container.initializeInDependencyOrder();
        console.log('✅ 统一初始化流程完成，应用已准备就绪。');

    } catch (error) {
        console.error('❌ 应用初始化过程中发生严重错误:', error);
        document.body.innerHTML = '<div style="color: red; text-align: center; padding: 50px;"><h1>Application Failed to Start</h1><p>A critical error occurred during initialization. Check the console for details.</p></div>';
    }
});
