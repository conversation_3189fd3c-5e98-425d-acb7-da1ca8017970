/**
 * ============================================================================
 * 主应用程序模块 (app.js) - 防AI幻觉核心声明
 * ============================================================================
 *
 * 【防AI幻觉声明】
 * 本文件是整个应用的UI控制器和业务流程编排中心，负责协调用户交互和后台服务。
 * 严禁AI基于类名推测功能，必须完整阅读所有方法才能理解完整的业务流程。
 * 本文件不直接处理数据逻辑，仅负责UI状态管理和用户操作响应。
 *
 * 【重复开发防护】
 * 警告：本项目已存在完整的MVC架构，严禁创建新的应用控制器。
 * 任何新的UI交互逻辑必须在本类中实现，而不是创建平行控制器。
 * 现有交互模式：[事件绑定 → 服务调用 → 结果展示] - 严禁修改此约定。
 *
 * 【竞态问题防护】
 * 多重异步操作竞态防护：
 * 1. 用户输入防抖：50ms防抖机制防止频繁触发自动检测
 * 2. 自动分析锁：autoAnalyzing标志防止并发自动分析
 * 3. 服务调用序列化：processInput方法中的步骤必须顺序执行
 * 4. DOM更新竞态：所有UI更新都通过showResult方法序列化
 * 防护措施：使用状态锁、异步队列、防抖函数确保操作原子性。
 *
 * 【声明与接口】
 * 导出：createApplicationModule (工厂函数)
 * 导入：无（依赖模块容器注入）
 * 类接口：ChannelDetectionEditor
 * 构造函数参数：container (必需，模块容器)
 * 公共方法：processInput, clearInput, editDetectionRules, editPromptSnippets
 * 私有方法：所有以_开头的内部方法
 *
 * 【依赖关系网络】
 * 直接依赖服务（通过容器注入）：
 *   - channelDetector：渠道检测服务 @REQUIRED
 *   - fieldMapper：字段映射服务 @REQUIRED
 *   - promptComposer：提示词合成服务 @REQUIRED
 *   - ruleEditor：规则编辑器服务 @OPTIONAL
 *   - promptEditor：提示词编辑器服务 @OPTIONAL
 *
 * 被依赖关系：
 *   - 被main.js注册为'app'模块
 *   - 被index.html的UI元素事件触发
 *   - 影响所有用户交互的响应性
 *
 * 【加载时机与生命周期】
 * 加载时机：模块容器初始化阶段按依赖顺序加载
 * 初始化时机：构造函数中立即初始化，bindEvents绑定UI事件
 * 生命周期：
 *   1. 模块创建 → 2. 服务缓存初始化 → 3. 事件绑定 → 4. 用户交互响应 → 5. 服务调用 → 6. 结果展示
 * 清理处理：无特殊清理逻辑（浏览器刷新时自然清理）
 *
 * 【技术栈与环境要求】
 * 运行环境：浏览器环境（依赖DOM API和事件系统）
 * 技术栈：原生JavaScript ES6+ 类语法，无框架依赖
 * 浏览器支持：现代浏览器（支持class语法、async/await、箭头函数）
 * DOM要求：依赖特定ID的HTML元素（inputContent, resultContainer等）
 *
 * 【核心功能说明】
 * 1. 用户输入处理：监听textarea输入，触发自动检测
 * 2. 手动处理流程：四步骤顺序处理（检测→组合→映射→展示）
 * 3. 自动分析功能：输入内容变化时自动触发分析
 * 4. 编辑器集成：提供规则编辑器和提示词编辑器的入口
 * 5. 状态管理：维护加载状态和自动分析锁
 * 6. 错误处理：统一的错误捕获和用户提示
 *
 * 【相关文件索引】
 * - index.html：提供UI元素和事件触发点
 * - main.js：负责本模块的注册和初始化
 * - channel-detector.js：渠道检测服务实现
 * - field-mapper.js：字段映射服务实现
 * - prompt-composer.js：提示词合成服务实现
 * - rule-editor.js：规则编辑器实现
 * - prompt-editor.js：提示词编辑器实现
 *
 * 【修改记录】
 * v1.0.0 (2025-08-31) - 重构版本
 *   - 重构为ES6类语法
 *   - 添加模块容器依赖注入
 *   - 完善错误处理机制
 *   - 添加详细的防AI幻觉注释系统
 *   - 优化自动检测性能（添加防抖和并发锁）
 *
 * 【使用说明】
 * 本模块通过模块容器自动初始化，无需手动实例化。
 * UI交互说明：
 * - 输入框：支持实时自动检测（>10字符触发）
 * - Ctrl+Enter：手动触发完整处理流程
 * - 按钮：提供手动触发和编辑器入口
 * 如果功能异常，请检查：
 * 1. HTML中是否包含必需的DOM元素
 * 2. 所有依赖服务是否正确注册到容器
 * 3. 浏览器控制台是否有错误信息
 *
 * ============================================================================
 */

/**
 * 主应用程序模块 - Refactored
 */
class ChannelDetectionEditor {
    constructor(container) {
        if (!container) {
            throw new Error("Module container is a required dependency.");
        }
        this.container = container;
        this.services = {}; // Service cache
        this.autoAnalyzing = false;
        this._initialized = false;
        // ✅ 移除 this.initialize() 调用，由容器控制初始化时机
    }

    initialize() {
        if (this._initialized) {
            console.log('⚠️ 渠道检测编辑器应用已初始化，跳过重复初始化');
            return;
        }
        
        console.log('渠道检测编辑器应用已初始化 (Main-Modal协同版)');
        this.bindEvents();
        this.initializeCoordinator();
        this._initialized = true;
    }

    /**
     * 初始化协同管理器
     */
    initializeCoordinator() {
        try {
            // 获取协同组件
            this.promptProcessor = this.getService('promptProcessor');
            this.promptManageModal = this.getService('promptManageModal');
            this.coordinator = this.getService('mainModalCoordinator');
            
            // 建立组件关联
            if (this.coordinator && this.promptProcessor && this.promptManageModal) {
                this.coordinator.linkComponents(this.promptProcessor, this.promptManageModal);
                console.log('✅ 协同管理器初始化完成');
            } else {
                console.warn('⚠️ 部分协同组件不可用，将使用传统模式');
            }
            
        } catch (error) {
            console.error('❌ 协同管理器初始化失败:', error);
        }
    }

    getService(serviceName) {
        if (this.services[serviceName]) {
            return this.services[serviceName];
        }
        const service = this.container.get(serviceName);
        if (!service) {
            throw new Error(`Service not found in container: ${serviceName}`);
        }
        this.services[serviceName] = service;
        return service;
    }

    bindEvents() {
        const inputTextarea = document.getElementById('inputContent');
        if(inputTextarea) {
            inputTextarea.addEventListener('input', (e) => {
                this.debounce(() => {
                    this.autoDetectFeatures(e.target.value);
                }, 50)();
            });

            inputTextarea.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();
                    this.processInput();
                }
            });
        }

        // Bind buttons
        document.getElementById('processInputBtn')?.addEventListener('click', () => this.processInput());
        document.getElementById('clearInputBtn')?.addEventListener('click', () => this.clearInput());
        document.getElementById('editRulesBtn')?.addEventListener('click', () => this.editDetectionRules());
        document.getElementById('editPromptBtn')?.addEventListener('click', () => this.editPromptSnippets());
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 处理用户输入
     */
    async processInput() {
        const inputText = this._getInputText();
        if (!inputText) {
            this.showResult('请输入内容后再处理', 'warning');
            return;
        }

        this.showLoading();

        try {
            if (this.promptProcessor) {
                await this._processWithPromptProcessor(inputText);
            } else {
                await this._processWithTraditionalMethod(inputText);
            }
        } catch (error) {
            this._handleProcessingError(error);
        }
    }

    /**
     * 获取输入文本
     * @private
     */
    _getInputText() {
        return document.getElementById('inputContent').value.trim();
    }

    /**
     * 使用PromptProcessor处理
     * @private
     */
    async _processWithPromptProcessor(inputText) {
        console.log('🚀 使用PromptProcessor协同处理...');
        const result = await this.promptProcessor.processOrderContent(inputText);

        if (result.success) {
            this._displayProcessingResults(inputText, result);
        } else {
            throw new Error(result.error);
        }
    }

    /**
     * 使用传统方法处理
     * @private
     */
    async _processWithTraditionalMethod(inputText) {
        console.log('⚠️ 回退到传统处理方式');
        await this.processInputTraditional(inputText);
    }

    /**
     * 显示处理结果
     * @private
     */
    _displayProcessingResults(inputText, result) {
        this.displayChannelResult(result.channel);
        this.displayPromptResult(result.prompt);
        this.displayFieldMapping(result.fields);
        this.showCompleteResult(inputText, result.channel, result.fields, result.prompt, result.fieldMapping);
        this.addViewMappingButton();
    }

    /**
     * 处理错误
     * @private
     */
    _handleProcessingError(error) {
        console.error('❌ 处理失败:', error);
        this.showResult(`处理失败: ${error.message}`, 'error');
    }

    /**
     * 传统处理方式（回退方案）
     * @param {string} inputText - 输入文本
     */
    async processInputTraditional(inputText) {
        const channelDetector = this.getService('channelDetector');
        const fieldMapper = this.getService('fieldMapper');
        const promptComposer = this.getService('promptComposer');

        // 步骤1: 渠道识别
        console.log('🔍 步骤1: 渠道识别');
        const channelResult = channelDetector.detectChannel(inputText);
        this.displayChannelResult(channelResult);

        // 步骤2: 提示词组合
        console.log('🔧 步骤2: 提示词组合');
        const detectedChannel = channelResult.channel || 'generic';
        const promptResult = await promptComposer.composePrompt(detectedChannel, null, inputText);
        if (!promptResult.success) {
            throw new Error('提示词组合失败: ' + promptResult.error);
        }
        this.displayPromptResult(promptResult);

        // 步骤3: 智能处理
        console.log('🤖 步骤3: 智能处理');
        const processingResult = await fieldMapper.processCompleteData(inputText, promptResult.composedPrompt);
        this.displayFieldMapping(processingResult);

        // 步骤4: 结果返回
        console.log('✅ 步骤4: 结果返回');
        this.showCompleteResult(inputText, channelResult, processingResult, promptResult);
    }

    /**
     * 自动检测功能
     * @param {string} text - 输入文本
     */
    async autoDetectFeatures(text) {
        if (!this._shouldPerformAutoDetection(text)) return;

        try {
            this.autoAnalyzing = true;
            await this._performChannelDetection(text);
            await this._performFieldAnalysis(text);
        } catch (error) {
            console.warn('⚠️ 自动检测失败:', error);
        } finally {
            this.autoAnalyzing = false;
        }
    }

    /**
     * 检查是否应该执行自动检测
     * @private
     */
    _shouldPerformAutoDetection(text) {
        return text && text.length >= 10 && !this.autoAnalyzing;
    }

    /**
     * 执行渠道检测
     * @private
     */
    async _performChannelDetection(text) {
        const channelDetector = this.getService('channelDetector');
        const channelResult = channelDetector.detectChannel(text);

        if (channelResult.confidence > 0.3) {
            this.displayChannelResult(channelResult, true);
        }

        return channelResult;
    }

    /**
     * 执行字段分析
     * @private
     */
    async _performFieldAnalysis(text) {
        const MIN_TEXT_LENGTH = 50;

        if (text.length > MIN_TEXT_LENGTH) {
            this.showAutoAnalysisStatus();
            const fieldMapper = this.getService('fieldMapper');
            const processingResult = await fieldMapper.processCompleteData(text);
            const channelResult = await this._performChannelDetection(text);

            this.displayFieldMapping(processingResult);
            this.showCompleteResult(text, channelResult, processingResult);
        }
    }

    /**
     * 打开规则编辑器
     */
    editDetectionRules() {
        try {
            const ruleEditor = this.getService('ruleEditor');
            ruleEditor.openEditor();
        } catch (error) {
            console.error('❌ 打开规则编辑器失败:', error);
            this.showResult(`打开规则编辑器失败: ${error.message}`, 'error');
        }
    }

    /**
     * 打开提示词编辑器
     */
    editPromptSnippets() {
        // 优先使用新的PromptManageModal
        if (this.promptManageModal) {
            console.log('🎛️ 打开提示词管理模态框...');
            this.promptManageModal.openModal({
                channel: this.promptProcessor?.lastDetectedChannel?.channel || 'generic'
            });
        } else {
            // 回退到传统编辑器
            try {
                const promptEditor = this.getService('promptEditor');
                if (promptEditor.openEditor) {
                    promptEditor.openEditor();
                } else {
                    // 如果没有 openEditor 方法，创建一个简单的编辑界面
                    this.createPromptEditorModal();
                }
            } catch (error) {
                console.error('❌ 打开提示词编辑器失败:', error);
                this.showResult(`打开提示词编辑器失败: ${error.message}`, 'error');
            }
        }
    }

    /**
     * 创建提示词编辑器模态框
     */
    createPromptEditorModal() {
        // 创建一个简单的提示词编辑界面
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
            background: rgba(0,0,0,0.7); z-index: 10000; display: flex; 
            align-items: center; justify-content: center;
        `;
        
        const content = document.createElement('div');
        content.style.cssText = `
            background: white; padding: 20px; border-radius: 8px; 
            max-width: 80%; max-height: 80%; overflow: auto;
        `;
        
        content.innerHTML = `
            <h2>提示词编辑器</h2>
            <p>提示词编辑器功能正在开发中...</p>
            <textarea style="width: 500px; height: 200px;" placeholder="在这里编辑提示词..."></textarea>
            <br><br>
            <button onclick="this.parentNode.parentNode.remove()">关闭</button>
        `;
        
        modal.appendChild(content);
        document.body.appendChild(modal);
    }

    /**
     * 显示结果信息
     */
    showResult(message, type = 'info') {
        const resultContainer = document.getElementById('resultContainer');
        if (resultContainer) {
            resultContainer.innerHTML = `
                <div class="result-item ${type}">
                    <strong>${message}</strong>
                </div>
            `;
        }
    }
    
    /**
     * 清空输入
     */
    clearInput() {
        const inputTextarea = document.getElementById('inputContent');
        if (inputTextarea) {
            inputTextarea.value = '';
        }
        this.showResult('输入已清空', 'info');
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const resultContainer = document.getElementById('resultContainer');
        if (resultContainer) {
            resultContainer.innerHTML = `
                <div class="result-item info">
                    <strong>🔄 正在处理...</strong>
                </div>
            `;
        }
    }

    /**
     * 显示渠道检测结果
     */
    displayChannelResult(channelResult, isAuto = false) {
        const prefix = isAuto ? '🤖 自动检测' : '🔍 检测结果';
        console.log(`${prefix}:`, channelResult);

        if (channelResult && channelResult.channel) {
            this.showResult(`${prefix}: ${channelResult.channel} (置信度: ${channelResult.confidence})`, 'success');
        } else {
            this.showResult(`${prefix}: 未识别到明确渠道`, 'warning');
        }
    }

    /**
     * 显示提示词组合结果
     */
    displayPromptResult(promptResult) {
        console.log('🔧 提示词组合结果:', promptResult);

        if (promptResult && promptResult.success) {
            this.showResult(`🔧 提示词组合完成 (渠道: ${promptResult.channelId})`, 'success');
        } else {
            this.showResult('🔧 提示词组合失败', 'warning');
        }
    }

    /**
     * 显示字段映射结果
     */
    displayFieldMapping(processingResult) {
        console.log('📋 字段映射结果:', processingResult);

        if (processingResult) {
            this.showResult('✅ 字段映射完成', 'success');
        }
    }

    /**
     * 显示完整处理结果
     */
    showCompleteResult(inputText, channelResult, processingResult, promptResult = null, fieldMapping = null) {
        console.log('✅ 处理完成:', {
            输入: inputText.substring(0, 100) + '...',
            渠道: channelResult,
            提示词: promptResult ? {
                渠道: promptResult.channelId,
                组合方式: promptResult.isModular ? '字段模块化' : '传统组合',
                成功: promptResult.success
            } : null,
            处理结果: processingResult,
            字段映射: fieldMapping
        });

        this.showResult('✅ 处理完成！查看控制台获取详细结果。', 'success');
        
        // 如果有字段映射，添加查看映射按钮
        if (fieldMapping && Object.keys(fieldMapping).length > 0) {
            this.addViewMappingButton();
        }
    }

    /**
     * 添加查看片段映射按钮
     */
    addViewMappingButton() {
        const resultContainer = document.getElementById('resultContainer');
        if (!resultContainer) return;

        // 检查是否已存在按钮
        if (document.getElementById('viewMappingBtn')) return;

        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'mapping-actions';
        buttonContainer.style.cssText = `
            margin-top: 15px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            padding-top: 15px;
        `;

        buttonContainer.innerHTML = `
            <button id="viewMappingBtn" class="btn btn-sm btn-purple" style="margin-right: 10px;">
                🎯 查看片段映射
            </button>
            <button id="openModalBtn" class="btn btn-sm btn-orange">
                🎛️ 管理提示词
            </button>
        `;

        resultContainer.appendChild(buttonContainer);

        // 绑定事件
        document.getElementById('viewMappingBtn').addEventListener('click', () => {
            this.viewFragmentMapping();
        });

        document.getElementById('openModalBtn').addEventListener('click', () => {
            this.editPromptSnippets();
        });
    }

    /**
     * 查看片段映射
     */
    viewFragmentMapping() {
        if (!this.promptProcessor) {
            alert('处理器不可用');
            return;
        }

        const fieldMapping = this.promptProcessor.getFieldToFragmentMapping();
        if (!fieldMapping || Object.keys(fieldMapping).length === 0) {
            alert('暂无字段映射数据，请先处理订单内容');
            return;
        }

        // 如果modal可用，直接打开并显示映射
        if (this.promptManageModal) {
            this.promptManageModal.openModal({
                showFieldMapping: true,
                channel: this.promptProcessor.lastDetectedChannel?.channel || 'generic'
            });
        } else {
            // 显示映射信息弹窗
            this.showMappingDialog(fieldMapping);
        }
    }

    /**
     * 显示映射信息对话框
     * @param {Object} fieldMapping - 字段映射数据
     */
    showMappingDialog(fieldMapping) {
        const mappingHtml = Object.entries(fieldMapping).map(([fieldName, mappingInfo]) => `
            <div class="mapping-row" style="margin-bottom: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <strong>${fieldName}</strong>
                        <div style="font-size: 12px; color: #666;">${mappingInfo.value || '无值'}</div>
                    </div>
                    <div style="text-align: right;">
                        <span style="background: ${mappingInfo.hasMapping ? '#4CAF50' : '#f44336'}; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">
                            ${mappingInfo.hasMapping ? `${mappingInfo.fragments.length}个片段` : '无匹配'}
                        </span>
                    </div>
                </div>
                ${mappingInfo.fragments.length > 0 ? `
                    <div style="margin-top: 8px; font-size: 12px;">
                        片段: ${mappingInfo.fragments.map(f => f.name).join(', ')}
                    </div>
                ` : ''}
            </div>
        `).join('');

        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.5); display: flex; justify-content: center;
            align-items: center; z-index: 9999;
        `;

        modal.innerHTML = `
            <div style="background: white; border-radius: 8px; padding: 20px; max-width: 600px; max-height: 80vh; overflow-y: auto;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                    <h3>🎯 字段片段映射</h3>
                    <button id="closeMappingDialog" style="background: none; border: none; font-size: 20px; cursor: pointer;">×</button>
                </div>
                <div>
                    ${mappingHtml}
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-purple" onclick="this.editPromptSnippets()">
                        🎛️ 打开提示词管理器
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定关闭事件
        modal.querySelector('#closeMappingDialog').addEventListener('click', () => {
            modal.remove();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    /**
     * 显示自动分析状态
     */
    showAutoAnalysisStatus() {
        console.log('🤖 自动分析中...');
    }
}

// 模块工厂函数
function createApplicationModule(container) {
    return new ChannelDetectionEditor(container);
}
