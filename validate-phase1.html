<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>阶段1验证 - 工厂函数测试</title>
    <style>
        .test-result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 4px; 
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🧪 阶段1验证：统一工厂模式</h1>
    <div id="results"></div>

    <!-- 按照index.html的顺序加载模块 -->
    <script src="js/core/module-container.js"></script>
    <script src="js/core/error-handler.js"></script>
    <script src="js/utils/local-storage-manager.js"></script>
    <script src="js/utils/crypto-utils.js"></script>
    <script src="data/hotels_by_region.js"></script>
    <script src="js/config/airport-data.js"></script>
    <script src="js/config/config.js"></script>
    <script src="js/services/gemini-config.js"></script>
    <script src="js/business/channel-detector.js"></script>
    <script src="js/services/address-translator.js"></script>
    <script src="js/business/channel-data-manager.js"></script>
    <script src="js/business/prompt-segmenter.js"></script>
    <script src="js/business/prompt-composer.js"></script>
    <script src="js/business/prompt-fragments.js"></script>
    <script src="js/business/field-mapper.js"></script>
    <script src="js/business/prompt-processor.js"></script>
    <script src="js/core/main-modal-coordinator.js"></script>
    <script src="js/ui/prompt-manage-modal.js"></script>

    <script>
        function addResult(message, type) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }

        function validatePhase1() {
            addResult('🚀 开始阶段1验证...', 'info');

            // 验证1: 检查工厂函数是否存在
            const tests = [
                {
                    name: 'createPromptProcessorModule工厂函数',
                    test: () => typeof window.createPromptProcessorModule === 'function'
                },
                {
                    name: 'createPromptManageModalModule工厂函数',
                    test: () => typeof window.createPromptManageModalModule === 'function'
                },
                {
                    name: 'createMainModalCoordinatorModule工厂函数',
                    test: () => typeof window.createMainModalCoordinatorModule === 'function'
                },
                {
                    name: '模块容器正常工作',
                    test: () => window.moduleContainer && typeof window.moduleContainer.register === 'function'
                },
                {
                    name: 'PromptProcessor类可用',
                    test: () => typeof PromptProcessor !== 'undefined'
                },
                {
                    name: 'PromptManageModal类可用', 
                    test: () => typeof PromptManageModal !== 'undefined'
                },
                {
                    name: 'MainModalCoordinator类可用',
                    test: () => typeof MainModalCoordinator !== 'undefined'
                }
            ];

            let passedTests = 0;
            tests.forEach(test => {
                try {
                    const passed = test.test();
                    if (passed) {
                        addResult(`✅ ${test.name}`, 'success');
                        passedTests++;
                    } else {
                        addResult(`❌ ${test.name} - 测试失败`, 'error');
                    }
                } catch (error) {
                    addResult(`❌ ${test.name} - 错误: ${error.message}`, 'error');
                }
            });

            // 总结
            addResult(`📊 验证完成: ${passedTests}/${tests.length} 项测试通过`, 
                passedTests === tests.length ? 'success' : 'error');

            if (passedTests === tests.length) {
                addResult('🎉 阶段1验证成功！所有工厂函数正常创建', 'success');
            } else {
                addResult('⚠️ 阶段1验证失败，需要修复问题', 'error');
            }
        }

        // 等待模块加载完成后执行验证
        setTimeout(validatePhase1, 2000);
    </script>
</body>
</html>