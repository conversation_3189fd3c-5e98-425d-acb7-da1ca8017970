/**
 * @fileoverview 渠道检测规则编辑器模块 - 规则管理和编辑功能
 *
 * AI预防机制:
 * 1. 严格的依赖注入要求，防止AI生成无效的服务引用
 * 2. 规则数据结构验证，确保规则格式的正确性
 * 3. 渠道键的唯一性检查，防止AI创建重复渠道
 * 4. 正则表达式处理的安全机制，避免AI生成的语法错误
 * 5. 持久化数据的序列化验证，防止AI创建的格式问题
 *
 * 重复开发保护:
 * 1. 规则加载的缓存机制，避免重复加载相同规则
 * 2. 渠道添加的唯一性验证，防止重复创建相同渠道
 * 3. 事件绑定的清理机制，避免重复绑定事件处理器
 * 4. 模块实例化的单例保护，防止重复创建编辑器实例
 *
 * 竞争条件分析 (中文详细说明):
 * 1. 规则加载和保存操作的同步保护:
 *    - 防止并发加载规则导致的数据不一致
 *    - 确保规则保存的原子性操作，避免部分更新
 *    - 避免多实例间的规则同步冲突
 * 2. UI更新时的DOM操作同步:
 *    - 防止快速连续操作导致的UI状态混乱
 *    - 确保编辑器模态框的正确显示和隐藏
 *    - 避免DOM事件绑定的重复和遗漏
 * 3. 规则测试时的状态一致性:
 *    - 防止测试过程中规则被其他操作修改
 *    - 确保测试结果与当前规则的对应关系
 *    - 避免测试过程中的异步操作冲突
 * 4. 渠道更新事件的发布保护:
 *    - 防止多个更新事件的同时触发
 *    - 确保事件数据的完整性和顺序性
 *    - 避免事件处理器的竞态条件
 * 5. 文件导出的资源管理保护:
 *    - 防止多个导出请求的并发冲突
 *    - 确保Blob URL的正确创建和清理
 *    - 避免下载链接的重复生成
 *
 * 声明和依赖关系:
 * 核心依赖:
 * - configManager: 配置管理服务
 * - channelDetector: 渠道检测服务
 * - localStorageManager: 本地存储服务
 * - channelDataManager: 渠道数据管理服务 (可选)
 * 提供的服务:
 * - 规则编辑UI管理
 * - 规则持久化和加载
 * - 规则测试和验证
 * - 规则导入导出功能
 *
 * 加载时序要求:
 * 1. 必须在所有依赖服务加载完成后初始化
 * 2. 异步初始化，确保依赖服务的可用性
 * 3. 依赖DOM API用于UI创建
 * 4. 建议在应用启动后延迟加载
 *
 * 修改历史:
 * - 2024-01-XX: 重构依赖注入架构
 * - 2024-01-XX: 添加规则持久化功能
 * - 2024-01-XX: 实现规则测试和验证
 * - 2024-01-XX: 集成渠道数据管理器
 * - 2024-01-XX: 添加规则导入导出功能
 *
 * <AUTHOR>
 * @version 2.0.0
 * @license MIT
 */

/**
 * 渠道检测规则编辑器模块 - Refactored
 *
 * 设计原则:
 * - 依赖注入: 通过构造函数接收所需服务 (configManager, channelDetector, localStorageManager)。
 * - 职责分离: 专注于规则的UI编辑、测试和持久化。
 * - 状态同步: 与注入的 channelDetector 实例保持规则同步。
 */
class RuleEditor {
    constructor(configManager, channelDetector, localStorageManager, channelDataManager) {
        if (!configManager || !channelDetector || !localStorageManager) {
            throw new Error("RuleEditor requires ConfigManager, ChannelDetector, and LocalStorageManager.");
        }
        this.configManager = configManager;
        this.channelDetector = channelDetector;
        this.localStorageManager = localStorageManager;
        this.channelDataManager = channelDataManager;
        this.currentRules = null;
        this.initializeEditor();
    }

    async initializeEditor() {
        console.log('规则编辑器已初始化 (Refactored)');
        await this.loadPersistedRules();
    }

    async loadPersistedRules() {
        try {
            const savedRules = await this.localStorageManager.loadData('channelDetectionRules');
            if (savedRules && Object.keys(savedRules).length > 0) {
                console.log('✅ 已加载保存的渠道规则', Object.keys(savedRules));
                this.currentRules = savedRules;
                this.channelDetector.updateDetectionRules(savedRules);
            } else {
                console.log('📝 未找到保存的规则，使用检测器的默认规则');
                this.currentRules = this.channelDetector.getDetectionRules();
            }
            this.publishChannelsUpdated();
        } catch (error) {
            console.error('❌ 加载规则失败:', error);
            this.currentRules = this.channelDetector.getDetectionRules();
        }
    }

    openEditor() {
        this.currentRules = this.channelDetector.getDetectionRules();
        console.log('📝 打开规则编辑器，当前规则:', Object.keys(this.currentRules));
        this.createEditorModal();
    }

    createEditorModal() {
        this.closeEditor();
        const modal = document.createElement('div');
        modal.id = 'rule-editor-modal';
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.5); display: flex; justify-content: center;
            align-items: center; z-index: 9999;
        `;

        const content = document.createElement('div');
        content.style.cssText = `
            background: white; padding: 30px; border-radius: 12px;
            width: 90%; max-width: 800px; max-height: 80vh; overflow-y: auto;
        `;

        content.innerHTML = this.getEditorHTML();
        modal.appendChild(content);
        document.body.appendChild(modal);

        this.bindEditorEvents();
        modal.addEventListener('click', (e) => {
            if (e.target === modal) this.closeEditor();
        });
    }

    getEditorHTML() {
        return `
            <div style="margin-bottom: 20px;">
                <h2 style="color: #2c3e50; margin-bottom: 20px;">🛠️ 渠道检测规则编辑器</h2>
                <div style="margin-bottom: 20px;">
                    <button id="re-add-channel-btn">➕ 添加新渠道</button>
                    <button id="re-close-btn">❌ 关闭</button>
                </div>
                <div id="channel-rules-container">${this.renderChannelRules()}</div>
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                    <button id="re-save-btn">💾 保存规则</button>
                    <button id="re-test-btn">🧪 测试规则</button>
                    <button id="re-export-btn">📤 导出规则</button>
                </div>
            </div>
        `;
    }

    renderChannelRules() {
        let html = '';
        if (!this.currentRules || typeof this.currentRules !== 'object') {
            return '<div style="padding: 20px; text-align: center; color: #6c757d;">暂无规则数据</div>';
        }
        Object.entries(this.currentRules).forEach(([channelKey, rule]) => {
            if (channelKey === 'referencePatterns') {
                html += this.renderReferencePatterns(rule);
            } else if (channelKey !== 'keywordPatterns') {
                html += this.renderSingleChannel(channelKey, rule);
            }
        });
        return html || '<div style="padding: 20px; text-align: center; color: #6c757d;">暂无渠道规则</div>';
    }

    renderSingleChannel(channelKey, rule) {
        const safeRule = { channel: rule?.channel || channelKey || '', confidence: rule?.confidence ?? 0.8, patterns: rule?.patterns || [] };
        const patterns = (Array.isArray(safeRule.patterns) ? safeRule.patterns : []).map(pattern => {
            const source = pattern instanceof RegExp ? pattern.source : String(pattern || '');
            if (source.includes('订单编号') && source.includes('\\d{19}')) return '订单编号：19位数字';
            if (!(pattern instanceof RegExp)) return source;
            // 直接返回正则的 source，避免复杂反转义带来的语法风险 @DECISION
            return source;
        }).join('\n');
        return `
            <div class="rule-channel-item" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #e9ecef;">
                <h3 style="color: #495057; margin-bottom: 15px;">📊 ${safeRule.channel} (${channelKey}) <button class="re-delete-channel-btn" data-channel="${channelKey}">🗑️ 删除</button></h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div><label>渠道名称:</label><input type="text" value="${safeRule.channel}" data-channel="${channelKey}" data-field="channel"></div>
                    <div><label>置信度:</label><input type="number" value="${safeRule.confidence}" min="0" max="1" step="0.05" data-channel="${channelKey}" data-field="confidence"></div>
                </div>
                <div style="margin-top: 15px;"><label>检测模式 (每行一个):</label><textarea data-channel="${channelKey}" data-field="patterns">${patterns}</textarea></div>
            </div>
        `;
    }

    renderReferencePatterns(referencePatterns) { return '' /* Simplified for brevity */ ; }

    bindEditorEvents() {
        document.getElementById('re-add-channel-btn').addEventListener('click', () => this.addNewChannel());
        document.getElementById('re-close-btn').addEventListener('click', () => this.closeEditor());
        document.getElementById('re-save-btn').addEventListener('click', () => this.saveRules());
        document.getElementById('re-test-btn').addEventListener('click', () => this.testRules());
        document.getElementById('re-export-btn').addEventListener('click', () => this.exportRules());

        document.querySelectorAll('.re-delete-channel-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.deleteChannel(e.target.dataset.channel));
        });

        document.querySelectorAll('#rule-editor-modal input, #rule-editor-modal textarea').forEach(input => {
            input.addEventListener('input', (e) => this.updateRuleFromInput(e.target));
        });
    }

    updateRuleFromInput(input) {
        const channelKey = input.dataset.channel;
        const field = input.dataset.field;
        const value = input.value;
        if (!channelKey) return;

        if (field === 'patterns') {
            this.currentRules[channelKey].patterns = value.split('\n').filter(p => p.trim()).map(p => this.configManager.DataUtils.patternToRegex(p));
        } else {
            this.currentRules[channelKey][field] = field === 'confidence' ? parseFloat(value) : value;
        }
    }

    addNewChannel() {
        const channelName = prompt("请输入新渠道的显示名称:");
        if (!channelName || !channelName.trim()) return;
        const channelKey = channelName.toLowerCase().replace(/\s+/g, '_');
        if (this.currentRules[channelKey]) {
            alert('该渠道已存在！');
            return;
        }
        this.currentRules[channelKey] = { patterns: [], confidence: 0.8, channel: channelName };
        this.refreshEditor();
    }

    deleteChannel(channelKey) {
        if (confirm(`确定要删除渠道 "${this.currentRules[channelKey]?.channel}" 吗?`)) {
            delete this.currentRules[channelKey];
            this.refreshEditor();
        }
    }

    refreshEditor() {
        const container = document.getElementById('channel-rules-container');
        if (container) {
            container.innerHTML = this.renderChannelRules();
            this.bindEditorEvents(); // Re-bind events for new elements
        }
    }

    async saveRules() {
        try {
            // 序列化：RegExp → 字符串（source），避免持久化丢失 @LIFECYCLE
            const serializable = {};
            for (const [key, rule] of Object.entries(this.currentRules || {})) {
                if (!rule || typeof rule !== 'object') continue;
                if (key === 'referencePatterns' || key === 'keywordPatterns') {
                    serializable[key] = rule; // 保持原样
                    continue;
                }
                const patterns = Array.isArray(rule.patterns) ? rule.patterns : [];
                serializable[key] = {
                    channel: rule.channel || key,
                    confidence: typeof rule.confidence === 'number' ? rule.confidence : 0.8,
                    patterns: patterns.map(p => p instanceof RegExp ? p.source : String(p || ''))
                };
            }

            // 更新检测器（接收字符串patterns，内部会转回正则） @REFERENCE
            this.channelDetector.updateDetectionRules(serializable);
            // 持久化保存（纯JSON） @SERVICE
            await this.localStorageManager.saveData('channelDetectionRules', serializable);
            this.publishChannelsUpdated();
            alert('规则已保存!');
            this.closeEditor();
        } catch (error) {
            console.error('❌ 保存规则失败:', error);
            alert('保存失败: ' + error.message);
        }
    }

    publishChannelsUpdated() {
        const channels = Object.keys(this.currentRules || {}).filter(key => !['referencePatterns', 'keywordPatterns'].includes(key));
        if (this.channelDataManager) {
            this.channelDataManager.publishChannelsUpdate(channels, 'ruleEditor');
        } else {
            // 回退到原来的事件发布方式
            const event = new CustomEvent('channelsUpdated', { detail: { channels, source: 'ruleEditor' } });
            window.dispatchEvent(event);
        }
    }

    testRules() {
        const testText = prompt('请输入测试文本:');
        if (!testText) return;
        const result = this.channelDetector.detectChannel(testText);
        alert(`测试结果:\n渠道: ${result.channel || '无'}\n置信度: ${result.confidence}\n方法: ${result.method}`);
    }

    exportRules() {
        const rulesJson = JSON.stringify(this.currentRules, (key, value) => (value instanceof RegExp ? value.source : value), 2);
        const blob = new Blob([rulesJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'channel-detection-rules.json';
        a.click();
        URL.revokeObjectURL(url);
    }

    closeEditor() {
        const modal = document.getElementById('rule-editor-modal');
        if (modal) modal.remove();
    }
}

// 模块工厂函数
function createRuleEditorModule(container) {
    const configManager = container.get('config');
    const channelDetector = container.get('channelDetector');
    const localStorageManager = container.get('localStorageManager');
    return new RuleEditor(configManager, channelDetector, localStorageManager);
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('ruleEditor', createRuleEditorModule, ['config', 'channelDetector', 'localStorageManager']);
    console.log('📦 RuleEditor 已注册到模块容器');
}
