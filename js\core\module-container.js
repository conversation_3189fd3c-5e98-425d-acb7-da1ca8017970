/**
 * 【模块容器架构设计与问题记录 - 2025-01-01】
 * 
 * 设计模式：
 * - 依赖注入容器，单例模式管理所有模块
 * - 支持循环依赖检测和递归解析
 * - 使用工厂函数模式创建模块实例
 * 
 * 当前发现的问题：
 * 1. 混合注册模式：
 *    - 标准模式：main.js中调用register(name, factory, dependencies)
 *    - 自注册模式：模块文件中直接调用container.register()
 *    - 导致注册时机不一致，依赖解析失败
 * 
 * 2. 时机问题：
 *    - 自注册在文件加载时（同步）：prompt-processor.js、prompt-manage-modal.js等
 *    - 集中注册在DOMContentLoaded时（异步）：main.js中的所有注册
 *    - 造成initialize()执行时部分依赖模块不存在
 * 
 * 3. 初始化策略问题：
 *    - initialize()方法会尝试初始化所有已注册模块（第257-264行）
 *    - 包括不完整的自注册模块（缺少依赖关系声明）
 *    - 当依赖模块不存在时抛出"未找到模块"错误
 * 
 * 4. 依赖解析机制：
 *    - get()方法递归解析依赖（第217-218行）
 *    - 当依赖链中任何模块缺失时整个链条失败
 *    - 缺少依赖完整性验证机制
 * 
 * 具体错误：
 * - promptProcessor期望'configManager'，但注册的是'config'
 * - promptManageModal依赖channelDataManager等，但时机不同步
 * - 自注册模块不在main.js的依赖图管理范围内
 * 
 * 建议解决方案：
 * 1. 统一注册模式：选择工厂模式或自注册模式之一
 * 2. 增加条件检查：避免重复注册同一模块
 * 3. 完善依赖图：将所有模块纳入统一管理
 * 4. 添加依赖验证：在initialize前检查依赖完整性
 * 5. 支持延迟初始化：允许依赖不满足时暂缓初始化
 * 
 * ============================================================================
 * 
 * 模块容器系统 (module-container.js) - 防AI幻觉核心声明
 * ============================================================================
 *
 * 【防AI幻觉声明】
 * 本文件是整个应用的依赖注入核心，负责所有模块的生命周期和依赖关系管理。
 * 严禁AI基于类名推测功能，必须完整阅读所有方法才能理解依赖注入机制。
 * 本文件不包含业务逻辑，仅提供基础设施服务。
 *
 * 【重复开发防护】
 * 警告：本项目已存在完整的依赖注入系统，严禁创建新的模块管理系统。
 * 任何新的模块依赖必须通过本容器注册，而不是创建全局变量或直接导入。
 * 现有注册模式：[name, factory, dependencies] - 严禁修改此约定。
 *
 * 【竞态问题防护】
 * 多重初始化竞态防护：
 * 1. 单例模式：每个模块只创建一次实例，防止重复初始化
 * 2. 循环依赖检测：loading Set防止递归调用导致的栈溢出
 * 3. 初始化锁：initialized标志防止并发初始化
 * 4. 异步初始化序列化：initialize方法确保所有模块按依赖顺序初始化
 * 防护措施：使用状态锁、依赖图分析、递归检测确保依赖解析的原子性。
 *
 * 【声明与接口】
 * 导出：ModuleContainer类、window.moduleContainer实例
 * 导入：无（纯基础设施代码）
 * 主要接口：
 *   - register(name, factory, dependencies)：注册模块
 *   - get(name)：获取模块实例
 *   - initialize()：初始化所有模块
 *   - dispose()：清理所有模块
 *   - validateDependencies()：验证依赖关系
 *   - getDependencyGraph()：获取依赖图
 *
 * 【依赖关系网络】
 * 直接依赖：无（基础设施层）
 *
 * 被依赖关系：
 *   - 被main.js作为核心基础设施使用
 *   - 被所有业务模块间接依赖（通过容器获取服务）
 *   - 被window.getService函数作为兼容层使用
 *   - 影响整个应用的模块加载和依赖解析
 *
 * 【加载时机与生命周期】
 * 加载时机：HTML解析阶段同步加载（必须在main.js之前）
 * 初始化时机：main.js中调用initialize()方法时异步初始化
 * 生命周期：
 *   1. 文件加载 → 2. 全局实例创建 → 3. 模块注册阶段 → 4. 依赖解析 → 5. 实例创建 → 6. 初始化完成 → 7. 运行时服务 → 8. 清理销毁
 * 清理处理：dispose方法清理所有实例和状态
 *
 * 【技术栈与环境要求】
 * 运行环境：浏览器环境（依赖window对象）
 * 技术栈：原生JavaScript ES6+ Map/Set，无框架依赖
 * 浏览器支持：现代浏览器（支持Map、Set、async/await）
 * 兼容性：提供全局函数回退机制支持旧版代码
 *
 * 【核心功能说明】
 * 1. 模块注册：支持工厂函数和依赖声明
 * 2. 依赖解析：递归解析模块依赖，支持循环依赖检测
 * 3. 生命周期管理：单例模式和初始化状态管理
 * 4. 错误处理：详细的错误信息和异常传播
 * 5. 兼容性支持：全局函数和向后兼容
 * 6. 调试支持：依赖图生成和验证功能
 *
 * 【相关文件索引】
 * - main.js：使用本容器进行模块注册和初始化
 * - 所有业务模块：通过本容器获取依赖服务
 * - index.html：负责本文件的加载顺序
 *
 * 【修改记录】
 * v1.0.0 (2025-08-31) - 重构版本
 *   - 重构为ES6类语法
 *   - 添加循环依赖检测
 *   - 完善错误处理机制
 *   - 添加详细的防AI幻觉注释系统
 *   - 优化性能和内存管理
 *
 * 【使用说明】
 * 模块注册语法：
 * container.register('moduleName', factoryFunction, ['dep1', 'dep2'])
 *
 * 模块获取语法：
 * const instance = container.get('moduleName')
 *
 * 初始化语法：
 * await container.initialize()
 *
 * 调试说明：
 * - 使用getDependencyGraph()查看依赖关系
 * - 使用validateDependencies()检查依赖错误
 * - 控制台会输出详细的加载日志
 *
 * ============================================================================
 */

/**
 * @fileoverview 模块容器系统 - 核心依赖注入管理器
 *
 * AI预防机制:
 * 1. 严格的模块注册和获取流程，防止AI生成无效模块引用
 * 2. 循环依赖检测算法，防止AI创建的无效依赖链
 * 3. 单例模式确保实例唯一性，避免AI创建多重实例
 * 4. 显式依赖声明，防止AI推测依赖关系
 * 5. 模块生命周期管理，防止AI遗漏初始化或清理逻辑
 *
 * 重复开发保护:
 * 1. 模块注册检查，防止重复注册同一模块
 * 2. 实例缓存机制，避免重复创建开销
 * 3. 依赖图分析，识别重复依赖模式
 * 4. 向后兼容性保持，防止破坏现有集成
 *
 * 竞争条件分析 (中文详细说明):
 * 1. 模块加载状态跟踪 (this.loading Set):
 *    - 防止并发加载同一模块导致的重复初始化
 *    - 检测循环依赖时的状态一致性保护
 *    - 确保模块创建的原子性操作
 * 2. 初始化锁机制 (this.initialized 标志):
 *    - 防止多线程环境下的重复初始化
 *    - 确保初始化过程的顺序执行
 *    - 避免初始化过程中的状态竞争
 * 3. 依赖解析时的递归锁:
 *    - 防止深度依赖链中的并发访问冲突
 *    - 确保依赖实例创建的线程安全
 *    - 避免依赖解析过程中的死锁情况
 * 4. 清理操作的同步保护:
 *    - 防止清理过程中的并发访问
 *    - 确保模块dispose方法的顺序执行
 *    - 避免清理时的数据竞争条件
 *
 * 声明和依赖关系:
 * 核心依赖: 无外部依赖，纯净的依赖注入容器
 * 提供的服务:
 * - ModuleContainer: 核心容器类
 * - registerModule: 全局模块注册函数
 * - getModule: 全局模块获取函数
 * - getService: 统一服务获取工具
 *
 * 加载时序要求:
 * 1. 必须在所有业务模块之前加载
 * 2. 建议在页面加载早期执行
 * 3. 异步初始化支持，确保依赖模块就绪
 * 4. 清理操作应在页面卸载时执行
 *
 * 修改历史:
 * - 2024-01-XX: Linus创建 - 实现基础依赖注入功能
 * - 2024-01-XX: 添加循环依赖检测和错误处理
 * - 2024-01-XX: 集成全局服务获取工具，支持向后兼容
 * - 2024-01-XX: 优化性能，添加模块缓存和状态管理
 *
 * <AUTHOR> (代码品味优化)
 * @version 2.1.0
 * @license MIT
 */

/**
 * 模块容器系统 - 核心依赖注入管理器
 *
 * 设计目标:
 * 1. 实现显式依赖注入
 * 2. 支持模块化测试
 * 3. 保持向后兼容性
 * 4. 防止AI幻觉和重复开发
 *
 * 作者：Linus (代码品味优化)
 */
class ModuleContainer {
    constructor() {
        this.modules = new Map();
        this.factories = new Map();
        this.loading = new Set();
        this.initialized = false;
    }
    
    /**
     * 注册模块工厂函数
     * @param {string} name - 模块名
     * @param {Function} factory - 工厂函数，接收容器实例作为参数
     * @param {Array} dependencies - 依赖列表
     */
    register(name, factory, dependencies = []) {
        if (typeof factory !== 'function') {
            throw new Error(`模块 ${name} 的工厂必须是函数`);
        }
        
        // 阶段2：重复注册检测（支持双轨制兼容）
        if (this.factories.has(name)) {
            console.warn(`⚠️ 模块 ${name} 已存在，跳过重复注册`);
            return;
        }
        
        this.factories.set(name, {
            factory,
            dependencies,
            singleton: true
        });
        
        console.log(`📦 注册模块: ${name}, 依赖: [${dependencies.join(', ')}]`);
    }
    
    /**
     * 获取模块实例
     * @param {string} name - 模块名
     * @returns {any} 模块实例
     */
    get(name) {
        if (this.loading.has(name)) {
            throw new Error(`检测到循环依赖: ${name}`);
        }
        
        // 已存在的实例直接返回
        if (this.modules.has(name)) {
            return this.modules.get(name);
        }
        
        const moduleConfig = this.factories.get(name);
        if (!moduleConfig) {
            throw new Error(`未找到模块: ${name}`);
        }
        
        this.loading.add(name);
        
        try {
            // 递归解析依赖
            const dependencies = moduleConfig.dependencies.map(dep => this.get(dep));
            
            // 创建模块实例
            const instance = moduleConfig.factory(this, ...dependencies);
            
            if (moduleConfig.singleton) {
                this.modules.set(name, instance);
            }
            
            console.log(`✅ 创建模块实例: ${name}`);
            return instance;
            
        } finally {
            this.loading.delete(name);
        }
    }
    
    /**
     * 检查模块是否存在
     * @param {string} name - 模块名
     * @returns {boolean}
     */
    has(name) {
        return this.factories.has(name);
    }
    
    /**
     * 初始化所有已注册的模块
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.initialized) {
            console.log('⚠️  容器已初始化，跳过重复初始化');
            return;
        }
        
        console.log('🚀 初始化模块容器...');
        
        // 获取所有模块来触发初始化
        for (const [name] of this.factories) {
            try {
                this.get(name);
            } catch (error) {
                console.error(`❌ 模块 ${name} 初始化失败:`, error);
                throw error;
            }
        }
        
        this.initialized = true;
        console.log('✅ 模块容器初始化完成');
    }

    /**
     * 按依赖顺序初始化所有模块
     * @returns {Promise<void>}
     */
    async initializeInDependencyOrder() {
        console.log('🔄 按依赖顺序初始化模块...');
        
        // 构建依赖图的拓扑排序
        const sorted = this.topologicalSort();
        
        for (const moduleName of sorted) {
            const instance = this.modules.get(moduleName);
            if (instance && typeof instance.initialize === 'function' && !instance._initialized) {
                console.log(`⏳ 初始化模块: ${moduleName}`);
                
                try {
                    if (instance.initialize.constructor.name === 'AsyncFunction') {
                        await instance.initialize();
                    } else {
                        instance.initialize();
                    }
                    
                    instance._initialized = true;
                    console.log(`✅ 模块初始化完成: ${moduleName}`);
                } catch (error) {
                    console.error(`❌ 模块 ${moduleName} 初始化失败:`, error);
                    throw new Error(`模块 ${moduleName} 初始化失败: ${error.message}`);
                }
            }
        }
        
        console.log('✅ 所有模块按依赖顺序初始化完成');
    }

    /**
     * 拓扑排序，确保依赖模块在被依赖模块之前初始化
     * @returns {Array<string>} 排序后的模块名数组
     */
    topologicalSort() {
        const visited = new Set();
        const result = [];
        
        const visit = (name) => {
            if (visited.has(name)) return;
            visited.add(name);
            
            const moduleConfig = this.factories.get(name);
            if (moduleConfig) {
                // 先访问依赖
                moduleConfig.dependencies.forEach(dep => visit(dep));
                // 再访问自己
                result.push(name);
            }
        };
        
        // 访问所有模块
        for (const [name] of this.factories) {
            visit(name);
        }
        
        return result;
    }
    
    /**
     * 清理容器
     */
    dispose() {
        // 调用模块的dispose方法（如果存在）
        for (const [name, instance] of this.modules) {
            if (instance && typeof instance.dispose === 'function') {
                try {
                    instance.dispose();
                    console.log(`🗑️  清理模块: ${name}`);
                } catch (error) {
                    console.error(`清理模块 ${name} 失败:`, error);
                }
            }
        }
        
        this.modules.clear();
        this.factories.clear();
        this.loading.clear();
        this.initialized = false;
        
        console.log('🧹 容器已清理');
    }
    
    /**
     * 获取模块依赖图
     * @returns {Object} 依赖关系图
     */
    getDependencyGraph() {
        const graph = {};
        for (const [name, config] of this.factories) {
            graph[name] = config.dependencies;
        }
        return graph;
    }
    
    /**
     * 验证依赖关系
     * @returns {Array} 错误列表
     */
    validateDependencies() {
        const errors = [];
        
        for (const [name, config] of this.factories) {
            for (const dep of config.dependencies) {
                if (!this.factories.has(dep)) {
                    errors.push(`模块 ${name} 依赖未找到: ${dep}`);
                }
            }
        }
        
        // 简单循环依赖检测
        const visited = new Set();
        const visiting = new Set();
        
        const checkCycle = (name) => {
            if (visiting.has(name)) {
                errors.push(`检测到循环依赖: ${name}`);
                return;
            }
            
            if (visited.has(name)) return;
            
            visiting.add(name);
            const config = this.factories.get(name);
            
            if (config) {
                for (const dep of config.dependencies) {
                    checkCycle(dep);
                }
            }
            
            visiting.delete(name);
            visited.add(name);
        };
        
        for (const [name] of this.factories) {
            checkCycle(name);
        }
        
        return errors;
    }
}

// 创建全局容器实例（兼容性）
window.moduleContainer = new ModuleContainer();

// 提供便捷的全局函数
window.registerModule = (name, factory, deps) => {
    window.moduleContainer.register(name, factory, deps);
};

window.getModule = (name) => {
    return window.moduleContainer.get(name);
};

/**
 * 统一服务获取工具 - 支持模块容器和全局变量回退
 * @param {string} serviceName - 服务名称
 * @returns {Object|null} 服务实例
 */
window.getService = function(serviceName) {
    // 优先从模块容器获取
    if (window.moduleContainer && window.moduleContainer.has && window.moduleContainer.has(serviceName)) {
        try {
            return window.moduleContainer.get(serviceName);
        } catch (error) {
            console.warn(`从模块容器获取${serviceName}服务失败:`, error);
        }
    }

    // 向后兼容：根据服务名映射到全局变量
    const serviceMap = {
        'gemini': 'geminiConfig',
        'promptFragmentManager': 'promptFragmentManager',
        'configManager': 'configManager',
        'localStorageManager': 'localStorageManager'
    };

    const globalName = serviceMap[serviceName] || serviceName;
    if (window[globalName]) {
        return window[globalName];
    }

    console.warn(`服务 ${serviceName} 不可用`);
    return null;
};

console.log('🏗️  模块容器系统已加载');