<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渠道检测编辑器 - 独立项目</title>
    <link rel="stylesheet" href="styles/main.css">

    <!-- 环境变量配置 -->
    <script>
        // 从环境变量或本地配置注入API密钥
        // 方法1: 从window对象注入 (推荐用于生产环境)
        // window.GEMINI_API_KEY = 'YOUR_API_KEY_HERE'; // 请设置您的API密钥

        // 方法2: 如果使用Node.js环境变量 (开发环境)
        if (typeof process !== 'undefined' && process.env) {
            window.GEMINI_API_KEY = window.GEMINI_API_KEY || process.env.GEMINI_API_KEY;
        }

        // 方法3: 从URL参数获取 (临时调试用)
        const urlParams = new URLSearchParams(window.location.search);
        const apiKeyFromUrl = urlParams.get('api_key');
        if (apiKeyFromUrl) {
            window.GEMINI_API_KEY = apiKeyFromUrl;
            console.log('🔑 从URL参数加载了API密钥 (仅用于调试)');
        }
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 渠道检测编辑器</h1>
            <p>独立项目 - 输入内容映射到表单 + 渠道检测特征编辑</p>
        </div>

        <div class="main-content">
            <div class="section input-section">
            <h2>📝 输入内容</h2>
            <textarea id="inputContent" placeholder="请输入订单内容..."></textarea>
                
                <div class="button-group">
                    <button class="btn-primary" id="processInputBtn">处理输入</button>
                    <button class="btn-secondary" id="clearInputBtn">清空</button>
                    <button class="btn-secondary btn-orange" id="editRulesBtn">🛠️ 编辑规则</button>
                    <button class="btn-secondary btn-purple" id="editPromptBtn">📝 编辑提示词</button>
                </div>
            </div>

            <div class="section">
                <h2>📊 处理结果</h2>
                <div class="result-section" id="resultContainer">
                    <div class="result-item">
                        <strong>等待处理...</strong>
                        <p>请输入内容并点击"处理输入"按钮</p>
                    </div>
                </div>

                <div class="channel-detection">
                    <h3>🔍 渠道检测结果</h3>
                    <div class="detection-result" id="channelResult">
                        <h4>未检测</h4>
                        <p>等待渠道检测...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="section section-wide">
            <div class="section-header">
                <h2>🗂️ 字段映射展示</h2>
                <div class="prompt-status-container">
                    <div id="prompt-status">
                        提示词片段: 加载中...
                    </div>
                    <button class="btn btn-sm btn-secondary btn-purple" id="quick-edit-prompts">
                        ⚡ 快速编辑
                    </button>
                </div>
            </div>
            <div class="field-mapping" id="fieldMapping">
                <div class="field-card">
                    <h4>customer_name</h4>
                    <p>客户姓名</p>
                    <div class="field-value" id="customer_name_value">-</div>
                </div>
                <div class="field-card">
                    <h4>customer_contact</h4>
                    <p>客户联系电话</p>
                    <div class="field-value" id="customer_contact_value">-</div>
                </div>
                <div class="field-card">
                    <h4>customer_email</h4>
                    <p>客户邮箱</p>
                    <div class="field-value" id="customer_email_value">-</div>
                </div>
                <div class="field-card">
                    <h4>ota_reference_number</h4>
                    <p>OTA参考编号</p>
                    <div class="field-value" id="ota_reference_number_value">-</div>
                </div>
                <div class="field-card">
                    <h4>flight_info</h4>
                    <p>航班信息</p>
                    <div class="field-value" id="flight_info_value">-</div>
                </div>
                <div class="field-card">
                    <h4>pickup</h4>
                    <p>接客地点</p>
                    <div class="field-value" id="pickup_value">-</div>
                </div>
                <div class="field-card">
                    <h4>destination</h4>
                    <p>目的地</p>
                    <div class="field-value" id="destination_value">-</div>
                </div>
                <div class="field-card">
                    <h4>passenger_number</h4>
                    <p>乘客数量</p>
                    <div class="field-value" id="passenger_number_value">-</div>
                </div>
                <div class="field-card">
                    <h4>luggage_number</h4>
                    <p>行李数量</p>
                    <div class="field-value" id="luggage_number_value">-</div>
                </div>
                <div class="field-card">
                    <h4>ota_price</h4>
                    <p>OTA平台价格</p>
                    <div class="field-value" id="ota_price_value">-</div>
                </div>
                <div class="field-card">
                    <h4>sub_category_id</h4>
                    <p>服务类型ID (2:接机, 3:送机, 4:包车)</p>
                    <div class="field-value" id="sub_category_id_value">-</div>
                </div>
                <div class="field-card">
                    <h4>extra_requirement</h4>
                    <p>额外要求/备注信息</p>
                    <div class="field-value" id="extra_requirement_value">-</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Refactored Script Loading Order -->
    <!-- 1. Module Container (Must be first) -->
    <script src="js/core/module-container.js"></script>

    <!-- 2. Core Services & Utils -->
    <script src="js/core/error-handler.js"></script>
    <script src="js/core/state-manager.js"></script>
    <script src="js/core/state-middlewares.js"></script>
    <script src="js/utils/local-storage-manager.js"></script>
    <script src="js/utils/crypto-utils.js"></script>
    <script src="data/hotels_by_region.js"></script>
    <script src="js/config/airport-data.js"></script>

    <!-- 3. Unified Config -->
    <script src="js/config/config.js"></script>

    <!-- 4. Core Business Logic Modules -->
    <script src="js/services/simple-cache.js"></script>
    <script src="js/services/gemini-config.js"></script>
    <script src="js/business/channel-detector.js"></script>
    <script src="js/services/address-translator.js"></script>
    <script src="js/business/channel-data-manager.js"></script>
    <script src="js/business/prompt-segmenter.js"></script>
    <script src="js/business/prompt-composer.js"></script>
    <script src="js/business/prompt-fragments.js"></script>
    <script src="js/business/field-mapper.js"></script>
    
    <!-- 5. New Coordinated Components -->
    <script src="js/business/prompt-processor.js"></script>
    <script src="js/core/main-modal-coordinator.js"></script>

    <!-- 6. UI/Editor Modules -->
    <script src="js/ui/rule-editor.js"></script>
    <script src="js/ui/prompt-editor.js"></script>
    <script src="js/ui/prompt-manage-modal.js"></script>

    <!-- 7. Cache System -->
    <script src="js/services/cache-manager.js"></script>
    <script src="js/services/cache-integration-adapter.js"></script>
    <script src="js/ui/cache-monitor-panel.js"></script>

    <!-- 8. Main Application Logic -->
    <script src="js/business/app.js"></script>

    <!-- 9. API配置测试 (可选，用于调试) -->
    <!-- <script src="test-api-config.js"></script> -->

    <!-- 10. Unified Initializer (Must be last) -->
    <script src="js/core/main.js"></script>
</body>
</html>