# 技术环境和开发设置

## 技术栈概述

**100%纯前端技术栈** - 零第三方依赖的现代化Web应用

项目完全基于Web标准技术实现，**不依赖任何构建工具**、框架库或运行时环境，真正做到了"开箱即用"的部署体验。

## 核心技术选型

### 前端技术

#### JavaScript (ES6+)
- **版本要求**: ES6+ (支持箭头函数、类、模块等现代特性)
- **兼容范围**: Chrome 60+、Firefox 55+、Safari 12+、Edge 79+
- **使用特性**:
  - 原生类和继承
  - Async/await异步处理
  - Map/Set数据结构
  - 解构赋值和模板字符串
  - 原生Promise和Fetch API

#### HTML5
- **语义化标签**: 大量使用语义化HTML结构
- **Web APIs**: 使用localStorage、sessionStorage、History API
- **响应式设计**: 支持移动端适配
- **无障碍性**: 基础的ARIA支持

#### CSS3
- **现代CSS特性**: Grid、Flexbox布局
- **CSS变量**: 主题色彩统一管理
- **动画效果**: 纯CSS实现的过渡和动画
- **响应式**: 媒体查询适配多设备

### AI集成

#### Gemini API
- **主要模型**: Gemini-2.5-Flash (快速文本处理)
- **优化模型**: Gemini-2.5-Pro (深度分析优化)
- **API配置**: 硬编码在gemini-config.js中
- **使用场景**: 智能字段提取、提示词优化、内容分析

### 数据存储

#### 本地存储策略
```javascript
// 三层存储架构
Memory Cache (Map) → sessionStorage → localStorage

存储用途:
- localStorage: 用户配置、渠道规则、提示词片段
- sessionStorage: 临时缓存、会话数据  
- Memory Cache: 热点数据、API响应缓存
```

## 开发环境设置

### 最简开发环境
```bash
# 无需安装任何依赖
# 直接在浏览器中打开文件即可
open channel-detection-editor/index.html
```

### 可选构建优化
```bash
# 仅用于性能优化，非必需
npm install terser --save-dev
node channel-detection-editor/build.js

# 生成优化版本
# index-optimized.html (单文件版本)
# app.min.js (压缩合并后的JS)
```

### 开发工具推荐
- **浏览器**: Chrome DevTools (最佳调试体验)
- **编辑器**: VS Code + HTML/CSS/JavaScript扩展
- **版本控制**: Git (已配置.gitignore)
- **服务器**: 任意HTTP服务器 (如Live Server扩展)

## 文件结构和加载

### 关键文件加载顺序 ⚠️ 重要
```html
<!DOCTYPE html>
<!-- 严格按以下顺序加载，不可调整 -->

<!-- 1. 依赖注入容器 (必须最先) -->
<script src="module-container.js"></script>

<!-- 2. 核心服务和工具 -->
<script src="error-handler.js"></script>
<script src="local-storage-manager.js"></script>
<script src="crypto-utils.js"></script>

<!-- 3. 数据层 -->
<script src="../hotels_by_region.js"></script>
<script src="airport-data.js"></script>
<script src="config.js"></script>

<!-- 4. 业务逻辑模块 -->
<script src="gemini-config.js"></script>
<script src="channel-detector.js"></script>
<script src="address-translator.js"></script>
<script src="channel-data-manager.js"></script>
<script src="prompt-segmenter.js"></script>
<script src="prompt-composer.js"></script>
<script src="prompt-fragments.js"></script>
<script src="field-mapper.js"></script>

<!-- 5. UI/编辑器模块 -->
<script src="rule-editor.js"></script>
<script src="prompt-editor.js"></script>

<!-- 6. 缓存系统 -->
<script src="cache-manager.js"></script>
<script src="cache-integration-adapter.js"></script>
<script src="cache-monitor-panel.js"></script>

<!-- 7. 主应用逻辑 -->
<script src="app.js"></script>

<!-- 8. 统一初始化器 (必须最后) -->
<script src="main.js"></script>
```

### 依赖关系图
```
module-container.js (根容器)
├── error-handler.js (全局错误处理)
├── config.js (配置管理)
│   ├── data.js (静态数据)
│   └── hotels_by_region.js (酒店数据)
├── channel-detector.js (渠道检测)
│   └── channel-data-manager.js (数据管理)
├── field-mapper.js (字段映射)
│   ├── gemini-config.js (AI配置)
│   └── address-translator.js (地址翻译)
├── prompt-*.js (提示词系统)
├── cache-*.js (缓存系统)
└── app.js (主应用)
```

## 性能优化策略

### 缓存策略配置
```javascript
// 缓存TTL配置
const CACHE_CONFIG = {
  GEMINI_API: 600000,        // 10分钟 - 最高优先级
  ADDRESS_TRANSLATION: 1800000,  // 30分钟 - 高优先级  
  CHANNEL_DETECTION: 300000,     // 5分钟 - 中优先级
  STATIC_CONFIG: 86400000       // 24小时 - 低优先级
};
```

### 懒加载机制
```javascript
// 模块按需加载
container.register('serviceName', factory, dependencies, {
  lazy: true  // 延迟到首次使用时才初始化
});
```

### 文件合并优化
```javascript
// 可选的构建优化
// 将12个JS文件合并为1个
// 压缩比例: 60-70% 大小减少
// HTTP请求: 12次 → 1次 (91.6%减少)
```

## 浏览器兼容性

### 支持的浏览器版本
| 浏览器 | 最低版本 | 状态 | 注意事项 |
|--------|----------|------|----------|
| Chrome | 60+ | ✅ 完全支持 | 推荐开发浏览器 |
| Firefox | 55+ | ✅ 完全支持 | 良好兼容性 |
| Safari | 12+ | ✅ 完全支持 | iOS设备良好 |
| Edge | 79+ | ✅ 完全支持 | 新版Edge |
| 移动端 | iOS 12+, Android 7+ | ✅ 响应式支持 | 触控优化 |

### 不支持的环境
- Internet Explorer (所有版本)
- Chrome < 60
- 非HTTPS环境下的某些API功能

## API配置和集成

### Gemini API配置
```javascript
// gemini-config.js
class GeminiConfig {
  constructor() {
    // ⚠️ 当前为硬编码，生产环境需要改进
    this.apiKey = 'hardcoded_api_key';
    this.baseUrl = 'https://generativelanguage.googleapis.com';
    this.models = {
      flash: 'gemini-2.5-flash',    // 快速处理
      pro: 'gemini-2.5-pro'        // 深度分析
    };
  }
}
```

### 网络请求处理
```javascript
// 使用原生Fetch API
const response = await fetch(url, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`
  },
  body: JSON.stringify(payload)
});
```

## 开发工作流

### 本地开发流程
```bash
1. 克隆代码到本地
2. 直接在浏览器中打开 index.html
3. 开始开发和调试
4. 使用浏览器DevTools进行调试
5. (可选) 运行构建脚本生成优化版本
```

### 调试技巧
```javascript
// 启用详细日志
window.DEBUG = true;

// 查看依赖关系图
console.log(window.container.getDependencyGraph());

// 缓存状态监控
window.cacheManager.getStats();

// 性能基准测试
window.performanceBenchmark.run();
```

### 快捷键配置
```javascript
// 开发者快捷键 (在页面中)
Ctrl+Shift+T  // 快速开发验证流程
Ctrl+Shift+D  // 开发工作流面板
Ctrl+Shift+P  // 性能基准测试

// 用户快捷键
Ctrl+Enter    // 快速处理当前输入
Ctrl+Shift+H  // 历史记录面板
Ctrl+Shift+B  // 批量处理模式
Ctrl+Shift+E  // 快速导出结果
```

## 测试环境

### 测试文件结构
```
tests/
├── integration/
│   ├── comprehensive-test-suite.html     # 25项综合测试
│   └── automated-test-runner.html        # 自动化测试执行
├── performance/
│   ├── cache-performance-test.html       # 缓存性能测试
│   └── workflow-efficiency-monitor.html  # 效率监控
└── unit/ (空目录，可扩展单元测试)
```

### 测试执行
```bash
# 1. 综合功能测试
open tests/integration/comprehensive-test-suite.html

# 2. 自动化测试
open tests/integration/automated-test-runner.html

# 3. 性能基准测试  
open tests/performance/cache-performance-test.html
```

## 部署配置

### 生产部署要求
```bash
# 最低部署要求
1. 支持HTTPS的Web服务器
2. 静态文件托管能力
3. 支持单页应用的路由配置 (可选)

# 推荐部署方案
- Nginx / Apache (传统服务器)
- GitHub Pages / Netlify (静态托管)
- CDN + 对象存储 (高可用方案)
```

### 环境变量配置 (推荐改进)
```javascript
// 当前: 硬编码配置 (需改进)
// 推荐: 环境变量注入
const config = {
  GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
  DEBUG_MODE: process.env.NODE_ENV === 'development'
};
```

## 开发约束和最佳实践

### 技术约束
1. **禁用第三方库**: 任何外部JavaScript库都不允许引入
2. **纯静态实现**: 不依赖后端服务器和数据库
3. **现代浏览器**: 针对支持ES6+的现代浏览器优化
4. **安全优先**: 所有外部输入必须验证和清理

### 编码规范
1. **文件头注释**: 每个文件必须包含依赖和用途说明
2. **依赖声明**: 明确声明模块依赖关系
3. **错误处理**: 所有异步操作必须有错误处理
4. **性能考虑**: 优先使用缓存，避免重复计算

### 维护原则
1. **向后兼容**: 新功能不破坏现有用户数据
2. **渐进增强**: 核心功能在所有支持浏览器中可用
3. **优雅降级**: API失败时提供备用方案
4. **监控友好**: 关键操作提供日志和指标