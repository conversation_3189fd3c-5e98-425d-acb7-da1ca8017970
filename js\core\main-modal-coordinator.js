/**
 * 【Main-Modal协同机制记录 - 2025-01-01】
 * 
 * 职责：
 * - 管理PromptProcessor与PromptManageModal之间的双向数据绑定
 * - 实现实时同步和事件中介
 * - 协调Main组件(核心处理引擎)与Modal组件(管理界面)的协同工作
 * 
 * 架构特点：
 * - 无外部依赖，使用纯事件驱动模式
 * - 采用自注册模式，直接注册到moduleContainer（第597行）
 * - 作为纯协调器，不依赖其他业务模块
 * 
 * 协同流程：
 * 1. linkComponents() 建立组件关联（第56行）
 *    - 双向绑定：processor.linkToModal(modal) + modal.linkToProcessor(processor)
 * 2. setupEventMediators() 设置事件中介（第87行）
 *    - 监听processor事件，转发给modal
 *    - 监听modal事件，转发给processor
 * 3. 实时双向数据同步
 *    - syncToModal()：将处理结果同步到Modal显示
 *    - receiveFromModal()：接收Modal的编辑更新
 * 
 * 关键方法：
 * - enableRealTimeSync()：启用实时同步模式
 * - manualSync()：手动触发同步（用于测试）
 * - getCoordinatorStatus()：获取协同状态信息
 * 
 * 注册模式：
 * - 使用自注册模式，在文件末尾直接调用moduleContainer.register()
 * - 无依赖，因此不存在依赖解析问题
 * - 作为纯协调服务，可以独立初始化
 * 
 * ============================================================================
 * 
 * Main-Modal协同管理器
 * 
 * 职责：
 * 1. 建立PromptProcessor和PromptManageModal之间的双向数据绑定
 * 2. 管理协同状态和事件通信
 * 3. 确保数据一致性和实时同步
 * 4. 处理协同工作流的编排和控制
 * 
 * 协同特性：
 * - 双向数据绑定：main和modal的状态实时同步
 * - 事件中介：处理组件间的事件通信
 * - 状态管理：维护协同工作的全局状态
 * - 错误处理：处理协同过程中的异常情况
 */
class MainModalCoordinator {
    constructor(eventManager) {
        // 依赖注入
        this.eventManager = eventManager || window.moduleContainer?.get('eventManager');

        // 组件实例
        this.promptProcessor = null;
        this.promptManageModal = null;

        // 协同状态
        this.isLinked = false;
        this.isRealTimeMode = true;
        this.currentWorkflow = null;

        // 事件移除函数存储
        this.eventRemovers = [];

        // 同步状态
        this.lastSyncTime = null;
        this.syncQueue = [];
        this.isSyncing = false;

        // 初始化事件监听
        this._initializeEventListeners();

        console.log('✅ MainModalCoordinator initialized');
    }

    /**
     * 建立组件关联
     * @param {Object} processor - PromptProcessor实例
     * @param {Object} modal - PromptManageModal实例
     */
    linkComponents(processor, modal) {
        try {
            console.log('🔗 开始建立Main-Modal组件关联...');
            
            if (!processor || !modal) {
                throw new Error('Processor和Modal组件都是必需的');
            }
            
            this.promptProcessor = processor;
            this.promptManageModal = modal;
            
            // 建立双向关联
            processor.linkToModal(modal);
            modal.linkToProcessor(processor);
            
            // 设置协同管理器作为中介
            this.setupEventMediators();
            
            // 启用实时同步模式
            this.enableRealTimeSync();
            
            this.isLinked = true;
            this.lastSyncTime = new Date();
            
            console.log('✅ Main-Modal组件关联建立成功');
            
            // 触发关联建立事件
            this.triggerCoordinatorEvent('components_linked', {
                processor: processor,
                modal: modal,
                realTimeMode: this.isRealTimeMode
            });
            
        } catch (error) {
            console.error('❌ 建立组件关联失败:', error);
            throw error;
        }
    }

    /**
     * 初始化事件监听器
     * @private
     */
    _initializeEventListeners() {
        if (!this.eventManager) {
            console.warn('⚠️ EventManager not available, falling back to native events');
            this._setupNativeEventListeners();
            return;
        }

        // 使用统一事件管理器
        const removeProcessorListener = this.eventManager.on('processor.update', (data) => {
            this.handleProcessorEvent(data);
        });

        const removeModalListener = this.eventManager.on('modal.event', (data) => {
            this.handleModalEvent(data);
        });

        // 存储移除函数
        this.eventRemovers.push(removeProcessorListener, removeModalListener);

        console.log('✅ 统一事件监听器设置完成');
    }

    /**
     * 设置原生事件监听器（降级方案）
     * @private
     */
    _setupNativeEventListeners() {
        const processorHandler = (event) => {
            this.handleProcessorEvent(event.detail);
        };
        window.addEventListener('prompt-processor-update', processorHandler);

        const modalHandler = (event) => {
            this.handleModalEvent(event.detail);
        };
        window.addEventListener('prompt-modal-event', modalHandler);

        // 存储移除函数
        this.eventRemovers.push(
            () => window.removeEventListener('prompt-processor-update', processorHandler),
            () => window.removeEventListener('prompt-modal-event', modalHandler)
        );
    }

    /**
     * 启用实时同步模式
     */
    enableRealTimeSync() {
        this.isRealTimeMode = true;
        
        if (this.promptProcessor) {
            this.promptProcessor.setRealTimeMode(true);
        }
        
        if (this.promptManageModal) {
            this.promptManageModal.isRealTimeMode = true;
        }
        
        console.log('🔄 实时同步模式已启用');
    }

    /**
     * 禁用实时同步模式
     */
    disableRealTimeSync() {
        this.isRealTimeMode = false;
        
        if (this.promptProcessor) {
            this.promptProcessor.setRealTimeMode(false);
        }
        
        if (this.promptManageModal) {
            this.promptManageModal.isRealTimeMode = false;
        }
        
        console.log('⏸️ 实时同步模式已禁用');
    }

    /**
     * 处理Processor事件
     * @param {Object} eventDetail - 事件详情
     */
    handleProcessorEvent(eventDetail) {
        const { type, data, timestamp } = eventDetail;
        
        console.log('📥 协同器收到Processor事件:', type);
        
        switch (type) {
            case 'processing_completed':
                this.handleProcessingCompleted(data, timestamp);
                break;
            case 'channel_detected':
                this.handleChannelDetected(data, timestamp);
                break;
            case 'fields_extracted':
                this.handleFieldsExtracted(data, timestamp);
                break;
            case 'fragments_updated':
                this.handleFragmentsUpdated(data, timestamp);
                break;
            case 'modal_sync':
                this.handleModalSync(data, timestamp);
                break;
            default:
                this.forwardEventToModal(type, data, timestamp);
        }
        
        // 更新同步时间
        this.updateSyncTime();
    }

    /**
     * 处理Modal事件
     * @param {Object} eventDetail - 事件详情
     */
    handleModalEvent(eventDetail) {
        const { type, data, timestamp } = eventDetail;
        
        console.log('📥 协同器收到Modal事件:', type);
        
        switch (type) {
            case 'modal_opened':
                this.handleModalOpened(data, timestamp);
                break;
            case 'modal_closed':
                this.handleModalClosed(data, timestamp);
                break;
            case 'channel_switched':
                this.handleChannelSwitched(data, timestamp);
                break;
            case 'fragment_updated':
                this.handleFragmentUpdated(data, timestamp);
                break;
            default:
                this.forwardEventToProcessor(type, data, timestamp);
        }
        
        // 更新同步时间
        this.updateSyncTime();
    }

    /**
     * 处理订单处理完成事件
     * @param {Object} data - 事件数据
     * @param {string} timestamp - 时间戳
     */
    handleProcessingCompleted(data, timestamp) {
        console.log('✅ 协同器处理：订单处理完成');
        
        if (!this.isLinked || !this.isRealTimeMode) return;
        
        // 如果modal已打开，触发数据同步
        if (this.promptManageModal && this.promptManageModal.isOpen) {
            this.syncDataToModal('processing_completed', data);
        }
        
        // 记录工作流状态
        this.updateWorkflowState('processing_completed', data);
        
        // 触发协同事件
        this.triggerCoordinatorEvent('sync_completed', {
            type: 'processing',
            data: data,
            timestamp: timestamp
        });
    }

    /**
     * 处理渠道检测事件
     * @param {Object} data - 事件数据
     * @param {string} timestamp - 时间戳
     */
    handleChannelDetected(data, timestamp) {
        console.log('🔍 协同器处理：渠道检测');
        
        if (!this.isLinked) return;
        
        // 同步到modal
        this.syncDataToModal('channel_detected', data);
        
        // 更新工作流状态
        this.updateWorkflowState('channel_detected', data);
    }

    /**
     * 处理字段提取事件
     * @param {Object} data - 事件数据
     * @param {string} timestamp - 时间戳
     */
    handleFieldsExtracted(data, timestamp) {
        console.log('⚡ 协同器处理：字段提取');
        
        if (!this.isLinked) return;
        
        // 同步字段映射数据到modal
        this.syncDataToModal('fields_extracted', data);
        
        // 如果modal已打开，触发字段映射显示
        if (this.promptManageModal && this.promptManageModal.isOpen) {
            this.promptManageModal.displayFieldMapping();
            this.promptManageModal.highlightMappedFragments();
        }
        
        // 更新工作流状态
        this.updateWorkflowState('fields_extracted', data);
    }

    /**
     * 处理片段更新事件
     * @param {Object} data - 事件数据
     * @param {string} timestamp - 时间戳
     */
    handleFragmentsUpdated(data, timestamp) {
        console.log('🔧 协同器处理：片段更新');
        
        if (!this.isLinked) return;
        
        // 通知processor重新组合提示词
        if (this.promptProcessor && this.promptProcessor.lastProcessedContent) {
            this.promptProcessor.recomposePrompt();
        }
        
        // 更新工作流状态
        this.updateWorkflowState('fragments_updated', data);
    }

    /**
     * 处理Modal打开事件
     * @param {Object} data - 事件数据
     * @param {string} timestamp - 时间戳
     */
    handleModalOpened(data, timestamp) {
        console.log('📂 协同器处理：Modal打开');
        
        if (!this.isLinked) return;
        
        // 同步当前处理状态到modal
        if (this.promptProcessor) {
            const currentState = {
                content: this.promptProcessor.lastProcessedContent,
                fields: this.promptProcessor.lastProcessedFields,
                channel: this.promptProcessor.lastDetectedChannel,
                fieldMapping: this.promptProcessor.getFieldToFragmentMapping()
            };
            
            this.syncDataToModal('initial_state', currentState);
        }
        
        // 启用实时同步
        this.enableRealTimeSync();
        
        this.triggerCoordinatorEvent('modal_opened', {
            modalState: data,
            syncEnabled: this.isRealTimeMode
        });
    }

    /**
     * 处理Modal关闭事件
     * @param {Object} data - 事件数据
     * @param {string} timestamp - 时间戳
     */
    handleModalClosed(data, timestamp) {
        console.log('📪 协同器处理：Modal关闭');
        
        // 保持实时同步模式，但不强制同步
        this.triggerCoordinatorEvent('modal_closed', {
            modalState: data,
            syncEnabled: this.isRealTimeMode
        });
    }

    /**
     * 处理渠道切换事件
     * @param {Object} data - 事件数据
     * @param {string} timestamp - 时间戳
     */
    handleChannelSwitched(data, timestamp) {
        console.log('🔀 协同器处理：渠道切换');
        
        if (!this.isLinked) return;
        
        // 通知processor渠道切换
        if (this.promptProcessor) {
            this.promptProcessor.receiveFromModal('channel_switched', data);
        }
        
        // 更新工作流状态
        this.updateWorkflowState('channel_switched', data);
    }

    /**
     * 同步数据到Modal
     * @param {string} eventType - 事件类型
     * @param {Object} data - 数据
     */
    syncDataToModal(eventType, data) {
        if (!this.promptManageModal || !this.isRealTimeMode) return;
        
        try {
            // 添加到同步队列
            this.syncQueue.push({
                target: 'modal',
                eventType: eventType,
                data: data,
                timestamp: new Date().toISOString()
            });
            
            // 处理同步队列
            this.processSyncQueue();
            
        } catch (error) {
            console.error('❌ 同步数据到Modal失败:', error);
        }
    }

    /**
     * 同步数据到Processor
     * @param {string} eventType - 事件类型
     * @param {Object} data - 数据
     */
    syncDataToProcessor(eventType, data) {
        if (!this.promptProcessor || !this.isRealTimeMode) return;
        
        try {
            // 添加到同步队列
            this.syncQueue.push({
                target: 'processor',
                eventType: eventType,
                data: data,
                timestamp: new Date().toISOString()
            });
            
            // 处理同步队列
            this.processSyncQueue();
            
        } catch (error) {
            console.error('❌ 同步数据到Processor失败:', error);
        }
    }

    /**
     * 处理同步队列
     */
    async processSyncQueue() {
        if (this.isSyncing || this.syncQueue.length === 0) return;
        
        this.isSyncing = true;
        
        try {
            while (this.syncQueue.length > 0) {
                const syncItem = this.syncQueue.shift();
                await this.executeSyncItem(syncItem);
            }
            
        } catch (error) {
            console.error('❌ 处理同步队列失败:', error);
        } finally {
            this.isSyncing = false;
        }
    }

    /**
     * 执行同步项
     * @param {Object} syncItem - 同步项
     */
    async executeSyncItem(syncItem) {
        const { target, eventType, data, timestamp } = syncItem;
        
        try {
            if (target === 'modal' && this.promptManageModal) {
                if (typeof this.promptManageModal.receiveFromProcessor === 'function') {
                    this.promptManageModal.receiveFromProcessor(eventType, data);
                }
            } else if (target === 'processor' && this.promptProcessor) {
                if (typeof this.promptProcessor.receiveFromModal === 'function') {
                    this.promptProcessor.receiveFromModal(eventType, data);
                }
            }
            
            console.log(`📤 同步完成: ${eventType} → ${target}`);
            
        } catch (error) {
            console.error(`❌ 执行同步项失败 (${target}):`, error);
        }
    }

    /**
     * 转发事件到Modal
     * @param {string} eventType - 事件类型
     * @param {Object} data - 事件数据
     * @param {string} timestamp - 时间戳
     */
    forwardEventToModal(eventType, data, timestamp) {
        if (this.isRealTimeMode) {
            this.syncDataToModal(eventType, data);
        }
    }

    /**
     * 转发事件到Processor
     * @param {string} eventType - 事件类型
     * @param {Object} data - 事件数据
     * @param {string} timestamp - 时间戳
     */
    forwardEventToProcessor(eventType, data, timestamp) {
        if (this.isRealTimeMode) {
            this.syncDataToProcessor(eventType, data);
        }
    }

    /**
     * 更新工作流状态
     * @param {string} stage - 工作流阶段
     * @param {Object} data - 相关数据
     */
    updateWorkflowState(stage, data) {
        this.currentWorkflow = {
            stage: stage,
            data: data,
            timestamp: new Date().toISOString(),
            components: {
                processor: !!this.promptProcessor,
                modal: !!this.promptManageModal && this.promptManageModal.isOpen
            }
        };
        
        console.log(`📊 工作流状态更新: ${stage}`);
    }

    /**
     * 更新同步时间
     */
    updateSyncTime() {
        this.lastSyncTime = new Date();
    }

    /**
     * 获取协同状态
     * @returns {Object} 协同状态信息
     */
    getCoordinatorStatus() {
        return {
            isLinked: this.isLinked,
            isRealTimeMode: this.isRealTimeMode,
            lastSyncTime: this.lastSyncTime,
            currentWorkflow: this.currentWorkflow,
            syncQueueLength: this.syncQueue.length,
            components: {
                processor: !!this.promptProcessor,
                modal: !!this.promptManageModal,
                modalOpen: this.promptManageModal?.isOpen || false
            }
        };
    }

    /**
     * 手动触发同步
     */
    manualSync() {
        console.log('🔄 手动触发协同同步...');
        
        if (!this.isLinked) {
            console.warn('⚠️ 组件未关联，无法同步');
            return;
        }
        
        // 强制处理同步队列
        this.processSyncQueue();
        
        // 同步当前状态
        if (this.promptProcessor && this.promptManageModal && this.promptManageModal.isOpen) {
            const currentState = {
                content: this.promptProcessor.lastProcessedContent,
                fields: this.promptProcessor.lastProcessedFields,
                channel: this.promptProcessor.lastDetectedChannel,
                fieldMapping: this.promptProcessor.getFieldToFragmentMapping()
            };
            
            this.syncDataToModal('manual_sync', currentState);
        }
        
        this.triggerCoordinatorEvent('manual_sync_completed', this.getCoordinatorStatus());
    }

    /**
     * 触发协同器事件
     * @param {string} eventType - 事件类型
     * @param {Object} data - 事件数据
     */
    triggerCoordinatorEvent(eventType, data) {
        const event = new CustomEvent('main-modal-coordinator', {
            detail: {
                type: eventType,
                data: data,
                timestamp: new Date().toISOString(),
                coordinator: this
            }
        });
        
        window.dispatchEvent(event);
        console.log(`📡 协同器事件触发: ${eventType}`);
    }

    /**
     * 销毁协同器
     */
    destroy() {
        console.log('🧹 销毁Main-Modal协同器...');
        
        // 移除事件监听器
        this.boundHandlers.forEach((handler, key) => {
            if (key === 'processor') {
                window.removeEventListener('prompt-processor-update', handler);
            } else if (key === 'modal') {
                window.removeEventListener('prompt-modal-event', handler);
            }
        });
        
        // 清理状态
        this.boundHandlers.clear();
        this.eventListeners.clear();
        this.syncQueue = [];
        
        // 断开组件关联
        this.promptProcessor = null;
        this.promptManageModal = null;
        this.isLinked = false;
        
        console.log('✅ 协同器销毁完成');
    }
}

// ============================================================================
// 自注册代码已移除 - 阶段3统一架构完成
// ============================================================================
// 
// 历史: 原有自注册代码已在阶段3中移除
// 原因: 统一为main.js集中注册模式，避免注册时机冲突
// 替代: 使用window.createMainModalCoordinatorModule工厂函数
// 注册位置: js/core/main.js 第154行

// ============================================================================
// 标准工厂函数 - 阶段1统一工厂模式
// ============================================================================

/**
 * 标准工厂函数：创建MainModalCoordinator实例
 * 用于main.js中的集中注册，与自注册形成双轨制兼容
 * @param {ModuleContainer} container - 模块容器实例（无依赖，不使用）
 * @returns {MainModalCoordinator} MainModalCoordinator实例
 */
window.createMainModalCoordinatorModule = function(container) {
    return new MainModalCoordinator();
};

// 导出类（用于其他模块导入）
if (typeof window !== 'undefined') {
    window.MainModalCoordinator = MainModalCoordinator;
}