/**
 * ============================================================================
 * 统一渠道数据管理服务 (channel-data-manager.js) - 防AI幻觉核心声明
 * ============================================================================
 *
 * 【防AI幻觉声明】
 * 本文件是渠道数据的统一管理中心，负责渠道规则和提示词的数据同步。
 * 严禁AI基于文件名推测功能，必须完整阅读所有方法才能理解数据同步的完整机制。
 * 本文件不直接处理业务逻辑，仅提供数据管理和同步服务。
 *
 * 【重复开发防护】
 * 警告：本项目已存在完整的渠道数据管理系统，严禁创建新的渠道管理模块。
 * 任何新的渠道相关数据必须通过本管理器同步，而不是直接操作存储。
 * 现有同步模式：[本地存储加载 → 数据合并 → 事件通知] - 严禁修改此约定。
 *
 * 【竞态问题防护】
 * 数据同步竞态防护：
 * 1. 异步加载序列化：loadChannels方法中的多个异步操作按顺序执行
 * 2. 事件处理原子化：handleChannelsUpdated和handlePromptsUpdated方法中的状态更新原子化
 * 3. 监听器通知序列化：notifyListeners方法对所有监听器依次通知
 * 4. Map操作线程安全：channels Map的所有操作都是原子的
 * 防护措施：使用async/await序列化、原子操作、事件队列确保数据同步的完整性。
 *
 * 【声明与接口】
 * 导出：ChannelDataManager类、createChannelDataManagerModule工厂函数
 * 导入：依赖localStorageManager模块容器服务
 * 主要接口：
 *   - constructor(localStorageManager)：依赖注入初始化
 *   - loadChannels()：从本地存储异步加载渠道数据
 *   - getAllChannels()：获取所有渠道名称列表
 *   - getChannelInfo(channelName)：获取特定渠道的详细信息
 *   - addListener(callback)：添加数据变更监听器
 *   - publishChannelsUpdate(channels, source)：发布渠道更新事件
 *   - publishPromptsUpdate(channels, source)：发布提示词更新事件
 *   - removeListener(callback)：移除数据变更监听器
 *   - notifyListeners(eventType, data)：通知所有监听器数据变更
 *
 * 【依赖关系网络】
 * 直接依赖：
 *   - localStorageManager：本地存储管理器 @REQUIRED
 *   - window对象：用于全局事件系统 @REQUIRED
 *
 * 被依赖关系：
 *   - 被main.js注册为渠道数据管理模块
 *   - 被ruleEditor依赖获取渠道列表
 *   - 被promptEditor依赖获取渠道信息
 *   - 被所有需要渠道数据的模块间接依赖
 *   - 影响整个应用的渠道数据一致性
 *
 * 【加载时机与生命周期】
 * 加载时机：模块容器初始化阶段按依赖顺序加载
 * 初始化时机：构造函数中立即开始，异步加载数据
 * 生命周期：
 *   1. 文件加载 → 2. 工厂函数注册 → 3. 依赖注入 → 4. 数据加载 → 5. 事件监听设置 → 6. 运行时数据同步 → 7. 事件发布 → 8. 监听器清理
 * 清理处理：通过removeListener方法清理事件监听器
 *
 * 【技术栈与环境要求】
 * 运行环境：浏览器环境（依赖localStorage和CustomEvent）
 * 技术栈：原生JavaScript ES6+，无框架依赖
 * 浏览器支持：现代浏览器（支持Map、Set、async/await、CustomEvent）
 * 存储要求：需要localStorage支持渠道数据持久化
 *
 * 【核心功能说明】
 * 1. 数据同步管理：渠道规则和提示词数据的双向同步
 * 2. 事件驱动架构：基于CustomEvent的事件通知机制
 * 3. 观察者模式：支持多个组件监听数据变更
 * 4. 持久化集成：与本地存储的完整集成
 * 5. 数据合并逻辑：智能合并不同来源的渠道数据
 * 6. 状态跟踪：记录每个渠道的规则和提示词状态
 * 7. 监听器管理：动态添加和移除事件监听器
 * 8. 错误处理：完整的异常处理和降级机制
 *
 * 【相关文件索引】
 * - main.js：负责本模块的注册
 * - rule-editor.js：使用本管理器获取渠道列表
 * - prompt-editor.js：使用本管理器获取渠道信息
 * - local-storage-manager.js：提供数据持久化服务
 *
 * 【修改记录】
 * v1.0.0 (2025-08-31) - 重构版本
 *   - 实现完整的渠道数据同步机制
 *   - 添加事件驱动的观察者模式
 *   - 完善数据合并和状态跟踪
 *   - 添加详细的防AI幻觉注释系统
 *   - 优化异步数据加载性能
 *   - 扩展监听器管理和错误处理
 *
 * 【使用说明】
 * 数据访问：通过模块容器获取实例后调用查询方法
 * 事件监听：使用addListener方法监听数据变更
 * 数据更新：使用publish方法发布数据变更事件
 * 状态查询：使用getChannelInfo方法获取渠道详细信息
 * 监听器管理：使用removeListener方法清理监听器
 * 事件通知：使用notifyListeners方法手动触发通知
 *
 * ============================================================================
 */

/**
 * 统一渠道数据管理服务
 * 负责管理渠道列表，确保提示词编辑器和渠道规则系统的数据同步
 */
class ChannelDataManager {
    constructor(localStorageManager) {
        this.localStorageManager = localStorageManager;
        this.channels = new Map();
        this.listeners = new Set();
        this._initialized = false;
        // ✅ 移除 this.initialize() 调用，由容器控制初始化时机
    }

    async initialize() {
        if (this._initialized) {
            console.log('⚠️ 渠道数据管理器已初始化，跳过重复初始化');
            return;
        }
        
        try {
            await this.loadChannels();
            this.setupEventListeners();
            this._initialized = true;
            console.log('ChannelDataManager initialized');
        } catch (error) {
            console.error('❌ 渠道数据管理器初始化失败:', error);
            this._initialized = true;
            throw error;
        }
    }

    async loadChannels() {
        try {
            // 从localStorage加载渠道规则
            const channelRules = await this.localStorageManager.loadData('channelDetectionRules') || {};
            // 从localStorage加载提示词片段
            const promptSnippets = await this.localStorageManager.loadData('promptSnippets') || {};

            // 合并渠道列表
            const ruleChannels = Object.keys(channelRules).filter(key =>
                !['referencePatterns', 'keywordPatterns'].includes(key)
            );
            const promptChannels = Object.keys(promptSnippets);

            const allChannels = new Set([...ruleChannels, ...promptChannels]);

            this.channels.clear();
            for (const channel of allChannels) {
                this.channels.set(channel, {
                    name: channel,
                    hasRules: ruleChannels.includes(channel),
                    hasPrompts: promptChannels.includes(channel),
                    lastUpdated: new Date().toISOString()
                });
            }

            console.log('Loaded channels:', Array.from(this.channels.keys()));
        } catch (error) {
            console.error('Failed to load channels:', error);
        }
    }

    setupEventListeners() {
        // 监听渠道规则更新事件
        window.addEventListener('channelsUpdated', (event) => {
            this.handleChannelsUpdated(event.detail);
        });

        // 监听提示词更新事件
        window.addEventListener('promptsUpdated', (event) => {
            this.handlePromptsUpdated(event.detail);
        });
    }

    handleChannelsUpdated(detail) {
        const { channels, source } = detail;
        console.log('Channels updated from', source, ':', channels);

        // 更新渠道列表
        for (const channel of channels) {
            if (!this.channels.has(channel)) {
                this.channels.set(channel, {
                    name: channel,
                    hasRules: true,
                    hasPrompts: false,
                    lastUpdated: new Date().toISOString()
                });
            } else {
                const existing = this.channels.get(channel);
                existing.hasRules = true;
                existing.lastUpdated = new Date().toISOString();
            }
        }

        this.notifyListeners('channelsUpdated', { channels: Array.from(this.channels.keys()) });
    }

    handlePromptsUpdated(detail) {
        const { channels, source } = detail;
        console.log('Prompts updated from', source, ':', channels);

        // 更新渠道列表
        for (const channel of channels) {
            if (!this.channels.has(channel)) {
                this.channels.set(channel, {
                    name: channel,
                    hasRules: false,
                    hasPrompts: true,
                    lastUpdated: new Date().toISOString()
                });
            } else {
                const existing = this.channels.get(channel);
                existing.hasPrompts = true;
                existing.lastUpdated = new Date().toISOString();
            }
        }

        this.notifyListeners('promptsUpdated', { channels: Array.from(this.channels.keys()) });
    }

    getAllChannels() {
        return Array.from(this.channels.keys());
    }

    getChannelInfo(channelName) {
        return this.channels.get(channelName) || null;
    }

    addListener(callback) {
        this.listeners.add(callback);
    }

    removeListener(callback) {
        this.listeners.delete(callback);
    }

    notifyListeners(eventType, data) {
        for (const listener of this.listeners) {
            try {
                listener(eventType, data);
            } catch (error) {
                console.error('Error notifying listener:', error);
            }
        }
    }

    // 发布渠道更新事件
    publishChannelsUpdate(channels, source) {
        const event = new CustomEvent('channelsUpdated', {
            detail: { channels, source }
        });
        window.dispatchEvent(event);
    }

    // 发布提示词更新事件
    publishPromptsUpdate(channels, source) {
        const event = new CustomEvent('promptsUpdated', {
            detail: { channels, source }
        });
        window.dispatchEvent(event);
    }
}

// 工厂函数
window.createChannelDataManagerModule = function(container) {
    const localStorageManager = container.get('localStorageManager');
    return new ChannelDataManager(localStorageManager);
};
