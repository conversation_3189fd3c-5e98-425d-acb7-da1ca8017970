/**
 * ============================================================================
 * 状态管理中间件 (state-middlewares.js) - 状态变更处理中间件
 * ============================================================================
 *
 * 【功能说明】
 * 提供状态管理的中间件系统，包括日志记录、验证、异步处理等功能。
 * 中间件按顺序执行，可以修改状态或阻止状态变更。
 *
 * 【中间件类型】
 * - 日志中间件：记录状态变更日志
 * - 验证中间件：验证状态变更的合法性
 * - 异步中间件：处理异步状态变更
 * - 缓存中间件：管理缓存相关状态
 * - 错误中间件：处理错误状态
 *
 * ============================================================================
 */

/**
 * 日志中间件 - 记录状态变更
 */
const loggingMiddleware = (oldState, newState, action) => {
    if (window.DEBUG_STATE_CHANGES) {
        console.group(`🔄 State Change: ${action.type}`);
        console.log('Action:', action);
        console.log('Old State:', oldState);
        console.log('New State:', newState);
        console.groupEnd();
    }
    return newState;
};

/**
 * 验证中间件 - 验证状态变更的合法性
 */
const validationMiddleware = (oldState, newState, action) => {
    const validators = {
        'processing.progress': (value) => {
            return typeof value === 'number' && value >= 0 && value <= 100;
        },
        'ui.theme': (value) => {
            return ['light', 'dark'].includes(value);
        },
        'app.preferences.language': (value) => {
            return ['zh-CN', 'en-US'].includes(value);
        }
    };

    if (action.type === 'SET_STATE') {
        const validator = validators[action.path];
        if (validator && !validator(action.value)) {
            console.error(`❌ 状态验证失败: ${action.path} = ${action.value}`);
            return oldState; // 阻止状态变更
        }
    }

    return newState;
};

/**
 * 异步处理中间件 - 处理异步状态变更
 */
const asyncMiddleware = (oldState, newState, action) => {
    // 处理异步操作的状态变更
    if (action.type === 'SET_STATE' && action.path === 'processing.isProcessing') {
        if (action.value === true) {
            // 开始处理时重置相关状态
            newState.processing.progress = 0;
            newState.processing.currentStep = null;
            newState.errors.current = null;
        } else if (action.value === false) {
            // 处理完成时更新进度
            newState.processing.progress = 100;
        }
    }

    return newState;
};

/**
 * 缓存状态中间件 - 管理缓存相关状态
 */
const cacheMiddleware = (oldState, newState, action) => {
    if (action.type === 'SET_STATE' && action.path.startsWith('cache.')) {
        // 自动计算缓存命中率
        const stats = newState.cache.stats;
        if (stats.hits !== undefined && stats.misses !== undefined) {
            const total = stats.hits + stats.misses;
            stats.hitRate = total > 0 ? (stats.hits / total * 100).toFixed(2) : 0;
        }
    }

    return newState;
};

/**
 * 错误处理中间件 - 管理错误状态
 */
const errorMiddleware = (oldState, newState, action) => {
    if (action.type === 'SET_STATE' && action.path === 'errors.current') {
        if (action.value) {
            // 添加到错误历史
            const errorHistory = newState.errors.history || [];
            errorHistory.push({
                error: action.value,
                timestamp: Date.now(),
                context: action.options?.context || null
            });
            
            // 限制错误历史大小
            if (errorHistory.length > 20) {
                errorHistory.shift();
            }
            
            newState.errors.history = errorHistory;
        }
    }

    return newState;
};

/**
 * 性能监控中间件 - 监控状态变更性能
 */
const performanceMiddleware = (oldState, newState, action) => {
    if (window.PERFORMANCE_MONITORING) {
        const startTime = performance.now();
        
        // 在下一个事件循环中记录性能
        setTimeout(() => {
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            if (duration > 10) { // 超过10ms的状态变更
                console.warn(`⚠️ 慢状态变更: ${action.type} (${duration.toFixed(2)}ms)`);
            }
        }, 0);
    }

    return newState;
};

/**
 * 通知中间件 - 管理通知状态
 */
const notificationMiddleware = (oldState, newState, action) => {
    if (action.type === 'SET_STATE' && action.path === 'ui.notifications') {
        // 自动为通知添加ID和时间戳
        if (Array.isArray(action.value)) {
            newState.ui.notifications = action.value.map(notification => ({
                id: notification.id || Date.now() + Math.random(),
                timestamp: notification.timestamp || Date.now(),
                ...notification
            }));
        }
    }

    return newState;
};

/**
 * 持久化中间件 - 管理需要持久化的状态
 */
const persistenceMiddleware = (oldState, newState, action) => {
    // 标记需要持久化的状态变更
    const persistentPaths = [
        'app.preferences',
        'ui.theme',
        'cache.enabled'
    ];

    if (action.type === 'SET_STATE') {
        const shouldPersist = persistentPaths.some(path => 
            action.path === path || action.path.startsWith(path + '.')
        );
        
        if (shouldPersist && action.options) {
            action.options.persist = true;
        }
    }

    return newState;
};

/**
 * 开发工具中间件 - 开发环境下的调试功能
 */
const devToolsMiddleware = (oldState, newState, action) => {
    if (window.__REDUX_DEVTOOLS_EXTENSION__ && window.NODE_ENV === 'development') {
        // 集成Redux DevTools
        window.__REDUX_DEVTOOLS_EXTENSION__.send(action, newState);
    }

    return newState;
};

/**
 * 获取默认中间件列表
 */
function getDefaultMiddlewares() {
    const middlewares = [
        validationMiddleware,
        asyncMiddleware,
        cacheMiddleware,
        errorMiddleware,
        notificationMiddleware,
        persistenceMiddleware
    ];

    // 开发环境添加额外中间件
    if (window.NODE_ENV === 'development' || window.DEBUG_MODE) {
        middlewares.push(loggingMiddleware);
        middlewares.push(performanceMiddleware);
        middlewares.push(devToolsMiddleware);
    }

    return middlewares;
}

/**
 * 创建自定义中间件
 */
function createCustomMiddleware(name, handler) {
    const middleware = (oldState, newState, action) => {
        try {
            return handler(oldState, newState, action) || newState;
        } catch (error) {
            console.error(`❌ 中间件 ${name} 执行失败:`, error);
            return newState;
        }
    };
    
    middleware.middlewareName = name;
    return middleware;
}

/**
 * 中间件组合器
 */
function composeMiddlewares(...middlewares) {
    return (oldState, newState, action) => {
        return middlewares.reduce((state, middleware) => {
            return middleware(oldState, state, action) || state;
        }, newState);
    };
}

// 导出
if (typeof window !== 'undefined') {
    window.StateMiddlewares = {
        loggingMiddleware,
        validationMiddleware,
        asyncMiddleware,
        cacheMiddleware,
        errorMiddleware,
        performanceMiddleware,
        notificationMiddleware,
        persistenceMiddleware,
        devToolsMiddleware,
        getDefaultMiddlewares,
        createCustomMiddleware,
        composeMiddlewares
    };
}
