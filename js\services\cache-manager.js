/**
 * ============================================================================
 * 统一缓存管理系统 (cache-manager.js) - 防AI幻觉核心声明
 * ============================================================================
 *
 * 【防AI幻觉声明】
 * 本文件是缓存系统的统一管理中心，负责所有数据的缓存存储和检索。
 * 严禁AI基于文件名推测功能，必须完整阅读所有缓存策略、存储后端和性能监控逻辑才能理解缓存机制的完整性。
 * 本文件不直接处理业务逻辑，仅提供缓存服务和管理功能。
 *
 * 【重复开发防护】
 * 警告：本项目已存在完整的缓存管理系统，严禁创建新的缓存模块。
 * 任何新的数据缓存需求必须通过本管理器实现，而不是创建分散的缓存逻辑。
 * 现有缓存模式：[多后端存储 → 策略配置 → 性能监控 → 自动清理] - 严禁修改此约定。
 *
 * 【竞态问题防护】
 * 缓存操作竞态防护：
 * 1. 异步操作序列化：get/set/delete方法中的多后端操作按顺序执行
 * 2. LRU清理原子化：cleanup方法中的LRU淘汰算法同步执行
 * 3. 统计更新同步：stats对象的所有更新操作原子化
 * 4. 定时器管理序列化：startAutoCleanup和startStatsReporting方法中的定时器创建按顺序执行
 * 防护措施：使用async/await序列化、原子操作、锁机制确保缓存操作的完整性。
 *
 * 【声明与接口】
 * 导出：CacheManager类、MemoryCache类、LocalStorageCache类、SessionStorageCache类
 * 导入：无直接依赖（使用浏览器原生存储API）
 * 主要接口：
 *   - constructor(options)：配置选项初始化
 *   - get(key, strategy)：从缓存获取数据
 *   - set(key, value, strategy, ttl)：设置缓存数据
 *   - delete(key, strategy)：删除缓存数据
 *   - clear(strategy)：清空指定策略的缓存
 *   - getStats()：获取缓存性能统计
 *   - setStrategy(name, config)：设置缓存策略
 *   - preload(data)：预加载数据到缓存
 *   - cleanup()：手动触发缓存清理
 *
 * 【依赖关系网络】
 * 直接依赖：
 *   - window.localStorage：浏览器本地存储API @OPTIONAL
 *   - window.sessionStorage：浏览器会话存储API @OPTIONAL
 *   - 定时器API：setInterval/clearInterval @REQUIRED
 *
 * 被依赖关系：
 *   - 被main.js注册为缓存管理模块
 *   - 被所有需要数据缓存的模块间接依赖
 *   - 被gemini-config.js依赖缓存API调用结果
 *   - 被address-translator.js依赖缓存翻译结果
 *   - 被channel-detector.js依赖缓存检测结果
 *   - 影响整个应用的性能和响应速度
 *
 * 【加载时机与生命周期】
 * 加载时机：模块容器初始化阶段按依赖顺序加载
 * 初始化时机：构造函数中立即初始化所有存储后端和策略
 * 生命周期：
 *   1. 文件加载 → 2. 工厂函数注册 → 3. 实例创建 → 4. 存储后端初始化 → 5. 缓存策略配置 → 6. 自动清理启动 → 7. 性能统计启动 → 8. 运行时缓存操作 → 9. 定时清理执行 → 10. 统计报告生成 → 11. 实例销毁
 * 清理处理：clearTimers方法清理所有活跃定时器
 *
 * 【技术栈与环境要求】
 * 运行环境：浏览器环境（依赖存储API和定时器）
 * 技术栈：原生JavaScript ES6+，无框架依赖
 * 浏览器支持：现代浏览器（支持Map、async/await、localStorage、sessionStorage）
 * 存储要求：支持localStorage和sessionStorage用于持久化缓存
 *
 * 【核心功能说明】
 * 1. 多后端存储：支持内存、localStorage、sessionStorage多种存储方式
 * 2. 智能策略：基于数据类型的不同缓存策略配置
 * 3. TTL管理：自动过期时间管理和清理机制
 * 4. LRU淘汰：最近最少使用的缓存淘汰算法
 * 5. 性能监控：详细的缓存命中率和响应时间统计
 * 6. 数据压缩：大对象自动压缩存储
 * 7. 自动清理：后台定时清理过期和低优先级缓存
 * 8. 调试支持：详细的调试日志和性能报告
 *
 * 【相关文件索引】
 * - main.js：负责本模块的注册
 * - gemini-config.js：使用缓存存储API调用结果
 * - address-translator.js：使用缓存存储翻译结果
 * - channel-detector.js：使用缓存存储检测结果
 * - cache-integration-adapter.js：缓存系统集成适配器
 * - cache-monitor-panel.js：缓存监控面板
 *
 * 【修改记录】
 * v1.0.0 (2025-08-31) - 初始版本
 *   - 实现多后端缓存存储系统
 *   - 添加TTL和LRU缓存淘汰机制
 *   - 完善性能统计和监控功能
 *   - 添加详细的防AI幻觉注释系统
 *   - 实现数据压缩和安全性保护
 *   - 支持缓存预热和后台清理
 *
 * 【使用说明】
 * 基本操作：使用get/set/delete方法进行缓存操作
 * 策略配置：使用setStrategy方法配置不同数据类型的缓存策略
 * 性能监控：使用getStats方法获取缓存性能统计
 * 手动清理：使用cleanup方法手动触发缓存清理
 * 数据预热：使用preload方法预加载重要数据
 * 调试模式：启用debugMode获取详细的缓存操作日志
 *
 * ============================================================================
 */

/**
 * 统一缓存管理系统 - 高效缓存架构
 *
 * === 功能特点 ===
 * - 多存储后端支持（内存/localStorage/sessionStorage）
 * - TTL（过期时间）和LRU（最近最少使用）机制
 * - 智能缓存策略和优先级管理
 * - 性能统计和监控
 * - 缓存预热和后台清理
 * - 数据压缩和安全性保护
 *
 * === 使用场景 ===
 * - Gemini API调用结果缓存（高价值）
 * - 地址翻译结果缓存（中高价值）
 * - 渠道检测结果缓存（中价值）
 * - 静态配置数据缓存（低价值，长期）
 *
 * === 性能目标 ===
 * - Gemini API调用命中率 > 70%
 * - 地址翻译计算节省 > 60%
 * - 缓存响应时间 < 5ms
 * - 内存使用控制在20MB内
 *
 * @ARCHITECTURE 缓存系统核心架构
 * @SERVICE 提供统一的缓存服务接口
 * @PERFORMANCE 优化系统性能表现
 */

class CacheManager {
    constructor(options = {}) {
        this.options = {
            // 默认存储后端优先级
            storageBackends: ['memory', 'localStorage'],
            
            // 全局TTL设置（毫秒）
            defaultTTL: 10 * 60 * 1000, // 10分钟
            
            // 内存缓存限制
            memoryLimit: 20 * 1024 * 1024, // 20MB
            maxEntries: 1000,
            
            // LRU配置
            enableLRU: true,
            lruCleanupThreshold: 0.8, // 80%使用率触发清理
            
            // 统计和监控
            enableStats: true,
            statsReportInterval: 60000, // 1分钟
            
            // 自动清理
            autoCleanup: true,
            cleanupInterval: 300000, // 5分钟
            
            // 数据压缩
            enableCompression: true,
            compressionThreshold: 1024, // 1KB以上启用压缩
            
            // 调试模式
            debugMode: false,
            
            ...options
        };
        
        // 存储后端实例
        this.backends = new Map();
        
        // 缓存策略配置
        this.cacheStrategies = new Map();
        
        // 性能统计
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0,
            evictions: 0,
            errors: 0,
            bytesStored: 0,
            avgResponseTime: 0,
            lastReportTime: Date.now()
        };
        
        // 活跃的定时器
        this.timers = new Set();
        
        this._initialized = false;
        // ✅ 移除 this.initialize() 调用，由容器控制初始化时机
    }
    
    /**
     * 初始化缓存管理器
     * @INIT 初始化所有缓存后端和策略
     */
    initialize() {
        if (this._initialized) {
            console.log('⚠️ 缓存管理器已初始化，跳过重复初始化');
            return;
        }
        
        try {
            console.log('🚀 初始化缓存管理系统...');
            
            // 初始化存储后端
            this.initializeBackends();
            
            // 配置默认缓存策略
            this.setupDefaultStrategies();
            
            // 启动自动清理
            if (this.options.autoCleanup) {
                this.startAutoCleanup();
            }
            
            // 启动性能统计
            if (this.options.enableStats) {
                this.startStatsReporting();
            }
            
            console.log('✅ 缓存管理系统初始化完成', {
                backends: Array.from(this.backends.keys()),
                strategies: Array.from(this.cacheStrategies.keys()),
                options: this.options
            });
            
            this._initialized = true;
            
        } catch (error) {
            console.error('❌ 缓存管理器初始化失败:', error);
            this._initialized = true; // 标记为已初始化，避免重复尝试
            throw error;
        }
    }
    
    /**
     * 初始化存储后端
     * @INIT 创建并配置各种存储后端
     */
    initializeBackends() {
        // 内存缓存后端（最快）
        if (this.options.storageBackends.includes('memory')) {
            this.backends.set('memory', new MemoryCache({
                maxSize: this.options.memoryLimit,
                maxEntries: this.options.maxEntries,
                enableLRU: this.options.enableLRU,
                debugMode: this.options.debugMode
            }));
            console.log('📦 内存缓存后端已创建');
        }
        
        // localStorage后端（持久化）
        if (this.options.storageBackends.includes('localStorage') && this.isLocalStorageAvailable()) {
            this.backends.set('localStorage', new LocalStorageCache({
                prefix: 'CDE_Cache_',
                enableCompression: this.options.enableCompression,
                compressionThreshold: this.options.compressionThreshold,
                debugMode: this.options.debugMode
            }));
            console.log('💾 localStorage缓存后端已创建');
        }
        
        // sessionStorage后端（会话级）
        if (this.options.storageBackends.includes('sessionStorage') && this.isSessionStorageAvailable()) {
            this.backends.set('sessionStorage', new SessionStorageCache({
                prefix: 'CDE_Session_',
                debugMode: this.options.debugMode
            }));
            console.log('🔄 sessionStorage缓存后端已创建');
        }
    }
    
    /**
     * 设置默认缓存策略
     * @CONFIG 为不同数据类型配置缓存策略
     */
    setupDefaultStrategies() {
        // Gemini API结果缓存策略（最高优先级）
        this.cacheStrategies.set('gemini', {
            ttl: 10 * 60 * 1000, // 10分钟
            priority: 10,
            backends: ['memory', 'localStorage'],
            enableCompression: true,
            autoRefresh: false,
            maxSize: 5 * 1024 * 1024 // 5MB限制
        });
        
        // 地址翻译结果缓存策略（高优先级）
        this.cacheStrategies.set('address', {
            ttl: 30 * 60 * 1000, // 30分钟
            priority: 8,
            backends: ['memory', 'localStorage'],
            enableCompression: false,
            autoRefresh: false,
            maxSize: 2 * 1024 * 1024 // 2MB限制
        });
        
        // 渠道检测结果缓存策略（中优先级）
        this.cacheStrategies.set('channel', {
            ttl: 5 * 60 * 1000, // 5分钟
            priority: 6,
            backends: ['memory'],
            enableCompression: false,
            autoRefresh: false,
            maxSize: 500 * 1024 // 500KB限制
        });
        
        // 静态配置缓存策略（低优先级，长期）
        this.cacheStrategies.set('config', {
            ttl: 24 * 60 * 60 * 1000, // 24小时
            priority: 4,
            backends: ['localStorage'],
            enableCompression: true,
            autoRefresh: false,
            maxSize: 1024 * 1024 // 1MB限制
        });
        
        // 默认缓存策略
        this.cacheStrategies.set('default', {
            ttl: this.options.defaultTTL,
            priority: 5,
            backends: ['memory'],
            enableCompression: false,
            autoRefresh: false,
            maxSize: 1024 * 1024
        });
        
        console.log('📋 缓存策略配置完成:', Array.from(this.cacheStrategies.keys()));
    }
    
    /**
     * 获取缓存数据
     * @SERVICE 统一的缓存获取接口
     * @param {string} key - 缓存键
     * @param {string} strategy - 缓存策略名称
     * @returns {Promise<any>} 缓存的数据或null
     */
    async get(key, strategy = 'default') {
        const startTime = Date.now();
        
        try {
            if (!key || typeof key !== 'string') {
                throw new Error('缓存键必须是非空字符串');
            }
            
            const strategyConfig = this.cacheStrategies.get(strategy) || this.cacheStrategies.get('default');
            const fullKey = this.buildCacheKey(key, strategy);
            
            // 按优先级顺序尝试各个后端
            for (const backendName of strategyConfig.backends) {
                const backend = this.backends.get(backendName);
                if (!backend) continue;
                
                try {
                    const result = await backend.get(fullKey);
                    if (result !== null) {
                        // 缓存命中
                        this.stats.hits++;
                        this.updateResponseTime(Date.now() - startTime);
                        
                        if (this.options.debugMode) {
                            console.log(`🎯 缓存命中 [${backendName}]:`, { key: fullKey, strategy });
                        }
                        
                        return result;
                    }
                } catch (error) {
                    console.warn(`⚠️ 后端 ${backendName} 读取失败:`, error.message);
                    this.stats.errors++;
                }
            }
            
            // 缓存未命中
            this.stats.misses++;
            this.updateResponseTime(Date.now() - startTime);
            
            if (this.options.debugMode) {
                console.log(`❌ 缓存未命中:`, { key: fullKey, strategy });
            }
            
            return null;
            
        } catch (error) {
            console.error('❌ 缓存获取失败:', error);
            this.stats.errors++;
            return null;
        }
    }
    
    /**
     * 设置缓存数据
     * @SERVICE 统一的缓存设置接口
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     * @param {string} strategy - 缓存策略名称
     * @param {number} ttl - 自定义TTL（可选）
     * @returns {Promise<boolean>} 是否设置成功
     */
    async set(key, value, strategy = 'default', ttl = null) {
        const startTime = Date.now();
        
        try {
            if (!key || typeof key !== 'string') {
                throw new Error('缓存键必须是非空字符串');
            }
            
            if (value === undefined) {
                throw new Error('缓存值不能为undefined');
            }
            
            const strategyConfig = this.cacheStrategies.get(strategy) || this.cacheStrategies.get('default');
            const fullKey = this.buildCacheKey(key, strategy);
            const finalTTL = ttl || strategyConfig.ttl;
            
            // 创建缓存条目
            const cacheEntry = {
                value: value,
                timestamp: Date.now(),
                ttl: finalTTL,
                strategy: strategy,
                priority: strategyConfig.priority,
                size: this.calculateSize(value)
            };
            
            // 检查大小限制
            if (cacheEntry.size > strategyConfig.maxSize) {
                console.warn('⚠️ 缓存条目超过大小限制:', {
                    key: fullKey,
                    size: cacheEntry.size,
                    limit: strategyConfig.maxSize
                });
                return false;
            }
            
            let successCount = 0;
            let totalAttempts = 0;
            
            // 写入所有配置的后端
            for (const backendName of strategyConfig.backends) {
                const backend = this.backends.get(backendName);
                if (!backend) continue;
                
                totalAttempts++;
                
                try {
                    const success = await backend.set(fullKey, cacheEntry, finalTTL);
                    if (success) {
                        successCount++;
                        
                        if (this.options.debugMode) {
                            console.log(`💾 缓存写入成功 [${backendName}]:`, { key: fullKey, strategy });
                        }
                    }
                } catch (error) {
                    console.warn(`⚠️ 后端 ${backendName} 写入失败:`, error.message);
                    this.stats.errors++;
                }
            }
            
            // 更新统计
            if (successCount > 0) {
                this.stats.sets++;
                this.stats.bytesStored += cacheEntry.size;
                this.updateResponseTime(Date.now() - startTime);
            }
            
            return successCount > 0;
            
        } catch (error) {
            console.error('❌ 缓存设置失败:', error);
            this.stats.errors++;
            return false;
        }
    }
    
    /**
     * 删除缓存数据
     * @SERVICE 统一的缓存删除接口
     * @param {string} key - 缓存键
     * @param {string} strategy - 缓存策略名称
     * @returns {Promise<boolean>} 是否删除成功
     */
    async delete(key, strategy = 'default') {
        try {
            if (!key || typeof key !== 'string') {
                throw new Error('缓存键必须是非空字符串');
            }
            
            const strategyConfig = this.cacheStrategies.get(strategy) || this.cacheStrategies.get('default');
            const fullKey = this.buildCacheKey(key, strategy);
            
            let successCount = 0;
            
            // 从所有后端删除
            for (const backendName of strategyConfig.backends) {
                const backend = this.backends.get(backendName);
                if (!backend) continue;
                
                try {
                    const success = await backend.delete(fullKey);
                    if (success) {
                        successCount++;
                        
                        if (this.options.debugMode) {
                            console.log(`🗑️ 缓存删除成功 [${backendName}]:`, { key: fullKey, strategy });
                        }
                    }
                } catch (error) {
                    console.warn(`⚠️ 后端 ${backendName} 删除失败:`, error.message);
                    this.stats.errors++;
                }
            }
            
            if (successCount > 0) {
                this.stats.deletes++;
            }
            
            return successCount > 0;
            
        } catch (error) {
            console.error('❌ 缓存删除失败:', error);
            this.stats.errors++;
            return false;
        }
    }
    
    /**
     * 清空指定策略的所有缓存
     * @SERVICE 批量清理接口
     * @param {string} strategy - 缓存策略名称
     * @returns {Promise<boolean>} 是否清理成功
     */
    async clear(strategy = null) {
        try {
            let successCount = 0;
            
            for (const [backendName, backend] of this.backends) {
                try {
                    const success = await backend.clear(strategy);
                    if (success) successCount++;
                } catch (error) {
                    console.warn(`⚠️ 后端 ${backendName} 清理失败:`, error.message);
                    this.stats.errors++;
                }
            }
            
            console.log(`🧹 缓存清理完成 [${strategy || 'all'}]:`, { successCount });
            return successCount > 0;
            
        } catch (error) {
            console.error('❌ 缓存清理失败:', error);
            this.stats.errors++;
            return false;
        }
    }
    
    /**
     * 获取缓存统计信息
     * @MONITORING 性能统计接口
     * @returns {Object} 统计信息
     */
    getStats() {
        const hitRate = this.stats.hits + this.stats.misses > 0 
            ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
            : '0.00';
            
        return {
            ...this.stats,
            hitRate: `${hitRate}%`,
            avgResponseTimeMs: this.stats.avgResponseTime.toFixed(2),
            bytesStoredMB: (this.stats.bytesStored / 1024 / 1024).toFixed(2),
            uptime: Date.now() - this.stats.lastReportTime
        };
    }
    
    /**
     * 获取缓存健康状态
     * @MONITORING 健康检查接口
     * @returns {Object} 健康状态
     */
    async getHealthStatus() {
        const health = {
            status: 'healthy',
            backends: {},
            issues: []
        };
        
        // 检查各个后端健康状态
        for (const [backendName, backend] of this.backends) {
            try {
                const backendHealth = await backend.getHealth();
                health.backends[backendName] = backendHealth;
                
                if (backendHealth.status !== 'healthy') {
                    health.status = 'warning';
                    health.issues.push(`${backendName}: ${backendHealth.message}`);
                }
            } catch (error) {
                health.backends[backendName] = { status: 'error', message: error.message };
                health.status = 'error';
                health.issues.push(`${backendName}: ${error.message}`);
            }
        }
        
        return health;
    }
    
    /**
     * 缓存预热
     * @OPTIMIZATION 预加载常用数据
     * @param {Array} warmupData - 预热数据列表
     * @returns {Promise<number>} 成功预热的条目数
     */
    async warmup(warmupData) {
        console.log('🔥 开始缓存预热...', { count: warmupData.length });
        
        let successCount = 0;
        const startTime = Date.now();
        
        for (const item of warmupData) {
            try {
                const { key, value, strategy = 'default', ttl = null } = item;
                const success = await this.set(key, value, strategy, ttl);
                if (success) successCount++;
            } catch (error) {
                console.warn('⚠️ 预热项目失败:', error.message);
            }
        }
        
        const duration = Date.now() - startTime;
        console.log('✅ 缓存预热完成:', { 
            successCount, 
            total: warmupData.length,
            duration: `${duration}ms`
        });
        
        return successCount;
    }
    
    /**
     * 构建完整缓存键
     * @UTIL 生成标准化的缓存键
     * @param {string} key - 原始键
     * @param {string} strategy - 策略名称
     * @returns {string} 完整缓存键
     */
    buildCacheKey(key, strategy) {
        return `${strategy}:${this.hashKey(key)}`;
    }
    
    /**
     * 对键进行哈希处理
     * @UTIL 生成键的哈希值
     * @param {string} key - 原始键
     * @returns {string} 哈希后的键
     */
    hashKey(key) {
        if (key.length <= 50) return key;
        
        // 简单哈希算法
        let hash = 0;
        for (let i = 0; i < key.length; i++) {
            const char = key.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return `${Math.abs(hash)}_${key.length}`;
    }
    
    /**
     * 计算数据大小
     * @UTIL 估算对象的内存大小
     * @param {any} data - 待计算的数据
     * @returns {number} 估算的字节大小
     */
    calculateSize(data) {
        if (data === null || data === undefined) return 0;
        
        try {
            const jsonStr = JSON.stringify(data);
            return new Blob([jsonStr]).size;
        } catch (error) {
            // 备用计算方法
            return JSON.stringify(data).length * 2; // 假设每字符2字节
        }
    }
    
    /**
     * 更新响应时间统计
     * @MONITORING 更新性能指标
     * @param {number} responseTime - 响应时间（毫秒）
     */
    updateResponseTime(responseTime) {
        if (this.stats.avgResponseTime === 0) {
            this.stats.avgResponseTime = responseTime;
        } else {
            // 指数移动平均
            this.stats.avgResponseTime = this.stats.avgResponseTime * 0.9 + responseTime * 0.1;
        }
    }
    
    /**
     * 启动自动清理
     * @MAINTENANCE 自动清理过期和低优先级数据
     */
    startAutoCleanup() {
        const cleanupTimer = setInterval(async () => {
            try {
                console.log('🧹 开始自动缓存清理...');
                
                let totalCleaned = 0;
                
                // 清理各个后端的过期数据
                for (const [backendName, backend] of this.backends) {
                    try {
                        const cleaned = await backend.cleanup();
                        totalCleaned += cleaned;
                        
                        if (cleaned > 0) {
                            console.log(`🗑️ ${backendName} 清理了 ${cleaned} 个过期条目`);
                        }
                    } catch (error) {
                        console.warn(`⚠️ ${backendName} 自动清理失败:`, error.message);
                    }
                }
                
                if (totalCleaned > 0) {
                    this.stats.evictions += totalCleaned;
                    console.log(`✅ 自动清理完成，共清理 ${totalCleaned} 个条目`);
                }
                
            } catch (error) {
                console.error('❌ 自动清理失败:', error);
            }
        }, this.options.cleanupInterval);
        
        this.timers.add(cleanupTimer);
    }
    
    /**
     * 启动统计报告
     * @MONITORING 定期报告性能统计
     */
    startStatsReporting() {
        const statsTimer = setInterval(() => {
            const stats = this.getStats();
            console.log('📊 缓存性能统计:', stats);
        }, this.options.statsReportInterval);
        
        this.timers.add(statsTimer);
    }
    
    /**
     * 检查localStorage可用性
     * @UTIL 检测localStorage支持
     * @returns {boolean} 是否可用
     */
    isLocalStorageAvailable() {
        try {
            const test = '__cache_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }
    
    /**
     * 检查sessionStorage可用性
     * @UTIL 检测sessionStorage支持
     * @returns {boolean} 是否可用
     */
    isSessionStorageAvailable() {
        try {
            const test = '__cache_test__';
            sessionStorage.setItem(test, test);
            sessionStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }
    
    /**
     * 销毁缓存管理器
     * @CLEANUP 清理所有资源
     */
    dispose() {
        // 清理定时器
        for (const timer of this.timers) {
            clearInterval(timer);
        }
        this.timers.clear();
        
        // 销毁后端
        for (const [backendName, backend] of this.backends) {
            try {
                if (typeof backend.dispose === 'function') {
                    backend.dispose();
                }
            } catch (error) {
                console.warn(`⚠️ 后端 ${backendName} 销毁失败:`, error.message);
            }
        }
        
        this.backends.clear();
        this.cacheStrategies.clear();
        
        console.log('🗑️ 缓存管理器已销毁');
    }
}

/**
 * 内存缓存实现
 * @BACKEND 高速内存存储后端
 */
class MemoryCache {
    constructor(options = {}) {
        this.options = {
            maxSize: 10 * 1024 * 1024, // 10MB
            maxEntries: 500,
            enableLRU: true,
            debugMode: false,
            ...options
        };
        
        this.cache = new Map();
        this.accessTimes = new Map(); // LRU访问时间记录
        this.currentSize = 0;
    }
    
    async get(key) {
        const entry = this.cache.get(key);
        if (!entry) return null;
        
        // 检查是否过期
        if (entry.timestamp + entry.ttl < Date.now()) {
            this.cache.delete(key);
            this.accessTimes.delete(key);
            this.currentSize -= entry.size;
            return null;
        }
        
        // 更新访问时间（LRU）
        if (this.options.enableLRU) {
            this.accessTimes.set(key, Date.now());
        }
        
        return entry.value;
    }
    
    async set(key, cacheEntry, ttl) {
        try {
            // 检查容量限制
            if (this.cache.size >= this.options.maxEntries) {
                this.evictLRU();
            }
            
            // 检查大小限制
            if (this.currentSize + cacheEntry.size > this.options.maxSize) {
                this.evictBySize(cacheEntry.size);
            }
            
            // 如果键已存在，先删除旧值
            if (this.cache.has(key)) {
                const oldEntry = this.cache.get(key);
                this.currentSize -= oldEntry.size;
            }
            
            // 添加新条目
            this.cache.set(key, cacheEntry);
            this.currentSize += cacheEntry.size;
            
            if (this.options.enableLRU) {
                this.accessTimes.set(key, Date.now());
            }
            
            return true;
        } catch (error) {
            console.error('内存缓存设置失败:', error);
            return false;
        }
    }
    
    async delete(key) {
        const entry = this.cache.get(key);
        if (!entry) return false;
        
        this.cache.delete(key);
        this.accessTimes.delete(key);
        this.currentSize -= entry.size;
        
        return true;
    }
    
    async clear(strategy = null) {
        if (strategy) {
            // 清理特定策略的缓存
            for (const [key, entry] of this.cache) {
                if (entry.strategy === strategy) {
                    this.cache.delete(key);
                    this.accessTimes.delete(key);
                    this.currentSize -= entry.size;
                }
            }
        } else {
            // 清理所有缓存
            this.cache.clear();
            this.accessTimes.clear();
            this.currentSize = 0;
        }
        return true;
    }
    
    async cleanup() {
        const now = Date.now();
        let cleanedCount = 0;
        
        // 清理过期条目
        for (const [key, entry] of this.cache) {
            if (entry.timestamp + entry.ttl < now) {
                this.cache.delete(key);
                this.accessTimes.delete(key);
                this.currentSize -= entry.size;
                cleanedCount++;
            }
        }
        
        return cleanedCount;
    }
    
    async getHealth() {
        return {
            status: 'healthy',
            entries: this.cache.size,
            sizeMB: (this.currentSize / 1024 / 1024).toFixed(2),
            utilizationPercent: ((this.cache.size / this.options.maxEntries) * 100).toFixed(1)
        };
    }
    
    evictLRU() {
        if (!this.options.enableLRU || this.accessTimes.size === 0) {
            // 随机删除一个
            const firstKey = this.cache.keys().next().value;
            if (firstKey) {
                this.delete(firstKey);
            }
            return;
        }
        
        // 找到最少访问的键
        let oldestKey = null;
        let oldestTime = Date.now();
        
        for (const [key, time] of this.accessTimes) {
            if (time < oldestTime) {
                oldestTime = time;
                oldestKey = key;
            }
        }
        
        if (oldestKey) {
            this.delete(oldestKey);
        }
    }
    
    evictBySize(neededSize) {
        const targetSize = this.options.maxSize * 0.8; // 清理到80%
        
        while (this.currentSize + neededSize > targetSize && this.cache.size > 0) {
            this.evictLRU();
        }
    }
}

/**
 * localStorage缓存实现
 * @BACKEND 持久化存储后端
 */
class LocalStorageCache {
    constructor(options = {}) {
        this.options = {
            prefix: 'Cache_',
            enableCompression: false,
            compressionThreshold: 1024,
            debugMode: false,
            ...options
        };
        
        this.storage = localStorage;
    }
    
    async get(key) {
        try {
            const fullKey = this.options.prefix + key;
            const stored = this.storage.getItem(fullKey);
            if (!stored) return null;
            
            let entry;
            try {
                entry = JSON.parse(stored);
            } catch (parseError) {
                // 清理损坏的条目
                this.storage.removeItem(fullKey);
                return null;
            }
            
            // 检查是否过期
            if (entry.timestamp + entry.ttl < Date.now()) {
                this.storage.removeItem(fullKey);
                return null;
            }
            
            // 解压缩（如果需要）
            let value = entry.value;
            if (entry.compressed) {
                value = this.decompress(value);
            }
            
            return value;
        } catch (error) {
            console.error('localStorage读取失败:', error);
            return null;
        }
    }
    
    async set(key, cacheEntry, ttl) {
        try {
            const fullKey = this.options.prefix + key;
            
            let value = cacheEntry.value;
            let compressed = false;
            
            // 压缩大对象
            if (this.options.enableCompression && 
                cacheEntry.size > this.options.compressionThreshold) {
                value = this.compress(value);
                compressed = true;
            }
            
            const storeEntry = {
                ...cacheEntry,
                value: value,
                compressed: compressed
            };
            
            this.storage.setItem(fullKey, JSON.stringify(storeEntry));
            return true;
        } catch (error) {
            // localStorage可能已满
            if (error.name === 'QuotaExceededError') {
                console.warn('⚠️ localStorage空间不足，尝试清理...');
                await this.cleanup();
                
                // 重试一次
                try {
                    const fullKey = this.options.prefix + key;
                    this.storage.setItem(fullKey, JSON.stringify(cacheEntry));
                    return true;
                } catch (retryError) {
                    console.error('localStorage设置失败（重试后）:', retryError);
                    return false;
                }
            }
            
            console.error('localStorage设置失败:', error);
            return false;
        }
    }
    
    async delete(key) {
        try {
            const fullKey = this.options.prefix + key;
            this.storage.removeItem(fullKey);
            return true;
        } catch (error) {
            console.error('localStorage删除失败:', error);
            return false;
        }
    }
    
    async clear(strategy = null) {
        try {
            if (strategy) {
                // 清理特定策略的缓存
                const keysToRemove = [];
                for (let i = 0; i < this.storage.length; i++) {
                    const key = this.storage.key(i);
                    if (key && key.startsWith(this.options.prefix)) {
                        try {
                            const stored = this.storage.getItem(key);
                            const entry = JSON.parse(stored);
                            if (entry.strategy === strategy) {
                                keysToRemove.push(key);
                            }
                        } catch (error) {
                            // 清理损坏的条目
                            keysToRemove.push(key);
                        }
                    }
                }
                
                keysToRemove.forEach(key => this.storage.removeItem(key));
            } else {
                // 清理所有缓存
                const keysToRemove = [];
                for (let i = 0; i < this.storage.length; i++) {
                    const key = this.storage.key(i);
                    if (key && key.startsWith(this.options.prefix)) {
                        keysToRemove.push(key);
                    }
                }
                
                keysToRemove.forEach(key => this.storage.removeItem(key));
            }
            
            return true;
        } catch (error) {
            console.error('localStorage清理失败:', error);
            return false;
        }
    }
    
    async cleanup() {
        const now = Date.now();
        let cleanedCount = 0;
        
        try {
            const keysToRemove = [];
            
            for (let i = 0; i < this.storage.length; i++) {
                const key = this.storage.key(i);
                if (!key || !key.startsWith(this.options.prefix)) continue;
                
                try {
                    const stored = this.storage.getItem(key);
                    const entry = JSON.parse(stored);
                    
                    // 检查是否过期
                    if (entry.timestamp + entry.ttl < now) {
                        keysToRemove.push(key);
                    }
                } catch (error) {
                    // 清理损坏的条目
                    keysToRemove.push(key);
                }
            }
            
            keysToRemove.forEach(key => {
                this.storage.removeItem(key);
                cleanedCount++;
            });
            
        } catch (error) {
            console.error('localStorage清理失败:', error);
        }
        
        return cleanedCount;
    }
    
    async getHealth() {
        let entryCount = 0;
        let totalSize = 0;
        
        try {
            for (let i = 0; i < this.storage.length; i++) {
                const key = this.storage.key(i);
                if (key && key.startsWith(this.options.prefix)) {
                    entryCount++;
                    const value = this.storage.getItem(key);
                    if (value) {
                        totalSize += value.length * 2; // 估算字节数
                    }
                }
            }
            
            return {
                status: 'healthy',
                entries: entryCount,
                sizeMB: (totalSize / 1024 / 1024).toFixed(2)
            };
        } catch (error) {
            return {
                status: 'error',
                message: error.message
            };
        }
    }
    
    compress(data) {
        // 简单的压缩实现（实际项目中可以使用更高效的压缩算法）
        return btoa(JSON.stringify(data));
    }
    
    decompress(compressedData) {
        try {
            return JSON.parse(atob(compressedData));
        } catch (error) {
            console.error('解压缩失败:', error);
            return null;
        }
    }
}

/**
 * sessionStorage缓存实现
 * @BACKEND 会话级存储后端
 */
class SessionStorageCache extends LocalStorageCache {
    constructor(options = {}) {
        super(options);
        this.storage = sessionStorage;
    }
}

// 模块工厂函数 - 替代全局实例创建
function createCacheManagerModule(container) {
    // 可以从容器中获取配置（如果需要）
    return new CacheManager();
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('cacheManager', createCacheManagerModule, []);
    console.log('📦 CacheManager已注册到模块容器');
}