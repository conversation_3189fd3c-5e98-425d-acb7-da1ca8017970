/* Extracted inline styles and main CSS from index.html */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 300;
}

.header p {
    font-size: 1.1em;
    opacity: 0.9;
}

/* Prompt editor modal styles */
.prompt-editor-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.prompt-editor-content {
    background: white;
    border-radius: 12px;
    width: 95%;
    max-width: 1400px;
    height: 85vh;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.prompt-editor-header {
    background: linear-gradient(135deg, #6f42c1 0%, #8e44ad 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.prompt-editor-header h2 {
    font-size: 1.5em;
    font-weight: 500;
}

.prompt-editor-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background 0.2s;
}

.prompt-editor-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.prompt-editor-body {
    flex: 1;
    display: grid;
    grid-template-columns: 300px 1fr 350px;
    gap: 0;
    overflow: hidden;
}

/* Left snippets panel */
.prompt-snippets-panel {
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
}

.snippets-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: white;
}

.snippets-search {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    margin-bottom: 10px;
}

.snippets-filters {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.filter-tag {
    padding: 4px 8px;
    background: #e9ecef;
    border: none;
    border-radius: 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.filter-tag.active {
    background: #6f42c1;
    color: white;
}

.snippets-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.snippet-item {
    padding: 12px;
    margin-bottom: 8px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.snippet-item:hover {
    border-color: #6f42c1;
    box-shadow: 0 2px 8px rgba(111, 66, 193, 0.1);
}

.snippet-item.active {
    border-color: #6f42c1;
    background: #f8f6ff;
}

.snippet-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.snippet-meta {
    font-size: 12px;
    color: #666;
    display: flex;
    justify-content: space-between;
}

/* Middle editor panel */
.prompt-editor-panel {
    background: white;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #e9ecef;
}

.editor-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.editor-form {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-input, .form-select, .form-textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
    outline: none;
    border-color: #6f42c1;
    box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.1);
}

.form-textarea {
    min-height: 200px;
    resize: vertical;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.form-actions {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    display: flex;
    gap: 10px;
}

/* Right AI suggestions panel */
.ai-suggestions-panel {
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
}

.ai-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: white;
}

.ai-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* Button styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #6f42c1;
    color: white;
}

.btn-primary:hover {
    background: #5a2d91;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    padding: 30px;
}

.section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 25px;
    border: 1px solid #e9ecef;
}

.section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5em;
    border-bottom: 2px solid #4facfe;
    padding-bottom: 10px;
}

.input-section textarea {
    width: 100%;
    min-height: 200px;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    font-family: 'Monaco', 'Menlo', monospace;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.input-section textarea:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.button-group {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

button {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.result-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    min-height: 300px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 13px;
    line-height: 1.5;
    overflow: auto;
    border: 1px solid #e9ecef;
}

.result-item {
    margin-bottom: 15px;
    padding: 15px;
    border-radius: 6px;
    background: #f8f9fa;
    border-left: 4px solid #4facfe;
}

.result-item.success {
    border-left-color: #28a745;
    background: #d4edda;
}

.result-item.warning {
    border-left-color: #ffc107;
    background: #fff3cd;
}

.result-item.error {
    border-left-color: #dc3545;
    background: #f8d7da;
}

.field-mapping {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 20px;
}

.field-card {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.field-card h4 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 14px;
}

.field-card p {
    color: #6c757d;
    font-size: 13px;
    margin: 0;
}

.field-value {
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    margin-top: 8px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 12px;
    color: #495057;
    border: 1px solid #e9ecef;
    min-height: 20px;
}

.field-value:not(:empty) {
    background: #e8f5e8;
    border-color: #28a745;
    color: #155724;
}

.channel-detection {
    margin-top: 20px;
}

.detection-result {
    background: #e8f4fd;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #4facfe;
    margin-top: 15px;
}

.detection-result h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.detection-details {
    font-size: 13px;
    color: #495057;
}

.confidence-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    margin: 10px 0;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe, #00f2fe);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Responsive design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .field-mapping {
        grid-template-columns: 1fr;
    }
}

/* Additional inline styles converted to classes */
.section-wide {
    grid-column: 1 / -1;
    margin: 30px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.prompt-status-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

#prompt-status {
    font-size: 14px;
    color: #666;
}

.btn-orange {
    background: #fd7e14;
    color: #fff;
}

.btn-purple {
    background: #6f42c1;
    color: #fff;
}

/* Main-Modal 协同组件样式 */
.has-field-mapping {
    border-left: 4px solid #4CAF50 !important;
    background: rgba(76, 175, 80, 0.05) !important;
}

.mapping-indicator {
    background: #4CAF50;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 8px;
    margin-left: 5px;
}

.mapping-actions {
    margin-top: 15px;
    text-align: center;
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.mapping-actions .btn {
    margin: 0 5px;
}

/* 字段映射显示样式 */
.field-mapping-display {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
}

.mapping-list {
    max-height: 300px;
    overflow-y: auto;
}

.mapping-item {
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.2s;
}

.mapping-item:hover {
    border-color: #6f42c1;
    box-shadow: 0 2px 4px rgba(111, 66, 193, 0.1);
}

.field-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.field-value {
    font-size: 12px;
    color: #666;
    font-family: monospace;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.fragments-info {
    font-size: 12px;
    color: #6f42c1;
}

/* 处理状态显示 */
.processing-status {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    font-size: 13px;
}

.progress-bar {
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 5px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe, #00f2fe);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* 协同状态显示 */
.sync-status {
    background: #f1f8e9;
    border: 1px solid #c8e6c9;
    border-radius: 6px;
    padding: 10px;
    margin-top: 15px;
}

.sync-indicators {
    font-size: 12px;
}

.sync-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 3px;
}

/* 测试模态框样式 */
.test-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.test-modal {
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.test-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.close-test-modal {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
}

.close-test-modal:hover {
    color: #333;
}

.test-section {
    margin-bottom: 20px;
}

.test-section h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 14px;
}

.test-content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
}

/* 选择模态框样式 */
.selection-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10001;
}

.selection-modal {
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 500px;
    max-height: 70vh;
    overflow-y: auto;
}

.selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.close-selection {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
}

.related-fragment-item {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.related-fragment-item:hover {
    border-color: #6f42c1;
    background: #f8f6ff;
}

.related-fragment-item strong {
    display: block;
    color: #333;
    margin-bottom: 5px;
}

.related-fragment-item p {
    margin: 0;
    font-size: 12px;
    color: #666;
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px 20px;
    border-radius: 4px;
    color: white;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.message-toast.success {
    background: #4CAF50;
}

.message-toast.error {
    background: #f44336;
}

.message-toast.info {
    background: #2196F3;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-state p {
    margin-bottom: 15px;
    font-size: 14px;
}

.placeholder {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 20px;
    font-size: 14px;
}

/* 渠道选择器样式 */
.channel-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    background: white;
    margin-bottom: 15px;
}

.channel-select:focus {
    outline: none;
    border-color: #6f42c1;
    box-shadow: 0 0 0 2px rgba(111, 66, 193, 0.1);
}

/* 片段过滤部分样式 */
.snippets-filters-section {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .prompt-editor-body {
        grid-template-columns: 250px 1fr 300px;
    }
}

@media (max-width: 768px) {
    .prompt-editor-body {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
    }
    
    .test-modal, .selection-modal {
        max-width: 95%;
        margin: 10px;
    }
}
