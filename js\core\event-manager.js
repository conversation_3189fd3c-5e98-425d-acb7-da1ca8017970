/**
 * ============================================================================
 * 事件管理器 (event-manager.js) - 统一事件处理系统
 * ============================================================================
 *
 * 【核心功能】
 * 提供统一的事件管理系统，包括事件队列、状态机管理、事件命名规范等功能。
 * 替换项目中分散的事件处理机制，提供更可靠的事件通信。
 *
 * 【设计特性】
 * - 事件队列：支持事件排队和批处理
 * - 状态机：管理复杂的事件流状态
 * - 命名规范：统一的事件命名约定
 * - 错误处理：事件处理失败的降级机制
 * - 调试支持：详细的事件日志和追踪
 *
 * 【事件命名规范】
 * - 模块.动作.状态：如 'processing.start.requested'
 * - 使用小写和点分隔符
 * - 动作词：start, stop, update, complete, error
 * - 状态词：requested, started, completed, failed
 *
 * ============================================================================
 */

/**
 * 事件管理器类
 */
class EventManager {
    constructor() {
        // 事件监听器存储
        this.listeners = new Map();
        
        // 事件队列
        this.eventQueue = [];
        this.isProcessingQueue = false;
        this.queueProcessingDelay = 0; // ms
        
        // 状态机
        this.stateMachines = new Map();
        
        // 事件历史（调试用）
        this.eventHistory = [];
        this.maxHistorySize = 100;
        
        // 配置
        this.config = {
            enableQueue: true,
            enableHistory: true,
            enableDebugLogs: false,
            maxListenersPerEvent: 50
        };
        
        console.log('✅ EventManager initialized');
    }

    /**
     * 添加事件监听器
     * @param {string} eventName - 事件名称
     * @param {Function} handler - 事件处理函数
     * @param {Object} options - 选项
     * @returns {Function} 移除监听器的函数
     */
    on(eventName, handler, options = {}) {
        if (!this.listeners.has(eventName)) {
            this.listeners.set(eventName, []);
        }
        
        const listeners = this.listeners.get(eventName);
        
        // 检查监听器数量限制
        if (listeners.length >= this.config.maxListenersPerEvent) {
            console.warn(`⚠️ 事件 ${eventName} 的监听器数量已达上限`);
            return () => {};
        }
        
        const listenerInfo = {
            handler,
            options,
            id: Date.now() + Math.random(),
            addedAt: Date.now()
        };
        
        listeners.push(listenerInfo);
        
        if (this.config.enableDebugLogs) {
            console.log(`📡 添加事件监听器: ${eventName}`);
        }
        
        // 返回移除函数
        return () => this.off(eventName, listenerInfo.id);
    }

    /**
     * 移除事件监听器
     * @param {string} eventName - 事件名称
     * @param {string|Function} handlerOrId - 处理函数或ID
     */
    off(eventName, handlerOrId) {
        const listeners = this.listeners.get(eventName);
        if (!listeners) return;
        
        const index = listeners.findIndex(listener => 
            listener.id === handlerOrId || listener.handler === handlerOrId
        );
        
        if (index !== -1) {
            listeners.splice(index, 1);
            if (listeners.length === 0) {
                this.listeners.delete(eventName);
            }
            
            if (this.config.enableDebugLogs) {
                console.log(`📡 移除事件监听器: ${eventName}`);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} eventName - 事件名称
     * @param {any} data - 事件数据
     * @param {Object} options - 选项
     */
    emit(eventName, data = null, options = {}) {
        const event = {
            name: eventName,
            data,
            timestamp: Date.now(),
            id: Date.now() + Math.random(),
            options
        };
        
        // 记录事件历史
        if (this.config.enableHistory) {
            this._addToHistory(event);
        }
        
        if (this.config.enableDebugLogs) {
            console.log(`📤 触发事件: ${eventName}`, data);
        }
        
        // 根据配置决定是否使用队列
        if (this.config.enableQueue && !options.immediate) {
            this._addToQueue(event);
        } else {
            this._processEvent(event);
        }
    }

    /**
     * 一次性事件监听器
     * @param {string} eventName - 事件名称
     * @param {Function} handler - 事件处理函数
     * @returns {Function} 移除监听器的函数
     */
    once(eventName, handler) {
        const removeListener = this.on(eventName, (data) => {
            handler(data);
            removeListener();
        }, { once: true });
        
        return removeListener;
    }

    /**
     * 等待事件触发（Promise版本）
     * @param {string} eventName - 事件名称
     * @param {number} timeout - 超时时间（ms）
     * @returns {Promise} Promise对象
     */
    waitFor(eventName, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                removeListener();
                reject(new Error(`等待事件 ${eventName} 超时`));
            }, timeout);
            
            const removeListener = this.once(eventName, (data) => {
                clearTimeout(timer);
                resolve(data);
            });
        });
    }

    /**
     * 批量触发事件
     * @param {Array} events - 事件数组
     */
    emitBatch(events) {
        events.forEach(({ name, data, options }) => {
            this.emit(name, data, { ...options, immediate: false });
        });
        
        // 立即处理队列
        this._processQueue();
    }

    /**
     * 创建状态机
     * @param {string} name - 状态机名称
     * @param {Object} config - 状态机配置
     */
    createStateMachine(name, config) {
        const stateMachine = new EventStateMachine(name, config, this);
        this.stateMachines.set(name, stateMachine);
        return stateMachine;
    }

    /**
     * 获取状态机
     * @param {string} name - 状态机名称
     */
    getStateMachine(name) {
        return this.stateMachines.get(name);
    }

    /**
     * 处理事件
     * @private
     */
    _processEvent(event) {
        const listeners = this.listeners.get(event.name);
        if (!listeners || listeners.length === 0) {
            if (this.config.enableDebugLogs) {
                console.log(`📭 无监听器: ${event.name}`);
            }
            return;
        }
        
        listeners.forEach(listenerInfo => {
            try {
                listenerInfo.handler(event.data, event);
            } catch (error) {
                console.error(`❌ 事件处理失败: ${event.name}`, error);
                
                // 触发错误事件
                this.emit('event.handler.error', {
                    originalEvent: event,
                    error: error,
                    listener: listenerInfo
                }, { immediate: true });
            }
        });
    }

    /**
     * 添加到事件队列
     * @private
     */
    _addToQueue(event) {
        this.eventQueue.push(event);
        
        if (!this.isProcessingQueue) {
            setTimeout(() => this._processQueue(), this.queueProcessingDelay);
        }
    }

    /**
     * 处理事件队列
     * @private
     */
    _processQueue() {
        if (this.isProcessingQueue || this.eventQueue.length === 0) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        try {
            while (this.eventQueue.length > 0) {
                const event = this.eventQueue.shift();
                this._processEvent(event);
            }
        } finally {
            this.isProcessingQueue = false;
        }
    }

    /**
     * 添加到事件历史
     * @private
     */
    _addToHistory(event) {
        this.eventHistory.push(event);
        
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.shift();
        }
    }

    /**
     * 获取事件统计
     */
    getStats() {
        const listenerCount = Array.from(this.listeners.values())
            .reduce((total, listeners) => total + listeners.length, 0);
        
        return {
            totalListeners: listenerCount,
            eventTypes: this.listeners.size,
            queueSize: this.eventQueue.length,
            historySize: this.eventHistory.length,
            stateMachines: this.stateMachines.size
        };
    }

    /**
     * 清理资源
     */
    dispose() {
        this.listeners.clear();
        this.eventQueue = [];
        this.eventHistory = [];
        this.stateMachines.forEach(sm => sm.dispose());
        this.stateMachines.clear();
        
        console.log('🧹 EventManager disposed');
    }
}

/**
 * 事件状态机类
 */
class EventStateMachine {
    constructor(name, config, eventManager) {
        this.name = name;
        this.eventManager = eventManager;
        this.currentState = config.initialState;
        this.states = config.states || {};
        this.transitions = config.transitions || {};
        
        // 监听状态转换事件
        this.eventManager.on(`statemachine.${name}.transition`, (data) => {
            this.handleTransition(data.from, data.to, data.event);
        });
    }

    /**
     * 处理状态转换
     */
    handleTransition(from, to, event) {
        const transition = this.transitions[`${from}->${to}`];
        if (transition && transition.guard && !transition.guard(event)) {
            console.warn(`⚠️ 状态转换被阻止: ${from} -> ${to}`);
            return false;
        }
        
        this.currentState = to;
        
        // 执行转换动作
        if (transition && transition.action) {
            transition.action(event);
        }
        
        // 触发状态变更事件
        this.eventManager.emit(`statemachine.${this.name}.state.changed`, {
            from,
            to,
            event
        });
        
        return true;
    }

    /**
     * 获取当前状态
     */
    getCurrentState() {
        return this.currentState;
    }

    /**
     * 清理资源
     */
    dispose() {
        this.eventManager.off(`statemachine.${this.name}.transition`);
    }
}

/**
 * 创建事件管理器模块
 */
function createEventManagerModule() {
    return new EventManager();
}

// 导出
if (typeof window !== 'undefined') {
    window.EventManager = EventManager;
    window.EventStateMachine = EventStateMachine;
    window.createEventManagerModule = createEventManagerModule;
}
