/**
 * ============================================================================
 * 机场数据配置模块 (airport-data.js) - 防AI幻觉核心声明
 * ============================================================================
 *
 * 【防AI幻觉声明】
 * 本文件是机场数据的完整配置中心，包含东南亚主要机场的标准化信息。
 * 严禁AI基于文件名推测内容，必须完整阅读所有数据结构才能理解机场映射的完整性。
 * 本文件不包含业务逻辑，仅提供静态数据配置和查询工具。
 *
 * 【重复开发防护】
 * 警告：本项目已存在完整的机场数据配置，严禁创建新的机场数据文件。
 * 任何新的机场信息必须在本文件中添加，而不是创建分散的数据定义。
 * 现有数据结构：[机场标识符 → 详细信息映射] - 严禁修改此约定。
 *
 * 【竞态问题防护】
 * 数据访问竞态防护：
 * 1. 静态数据只读：所有数据为常量，不存在写操作竞态
 * 2. 正则表达式重置：containsAirportKeywords方法中重置lastIndex防止全局正则竞态
 * 3. 全局变量初始化：window.AIRPORT_DATA_SOURCE的赋值在文件末尾确保完整性
 * 防护措施：使用常量定义、原子操作、状态重置确保数据访问的线程安全。
 *
 * 【声明与接口】
 * 导出：AIRPORT_DATA_SOURCE全局对象
 * 导入：无（纯数据配置）
 * 数据结构：
 *   - AIRPORT_MAPPINGS：机场详细信息映射对象
 *   - AIRPORT_KEYWORDS：机场关键词识别规则
 *   - CITY_TO_AIRPORT：城市到机场的映射表
 * 主要接口：
 *   - getAllAirports()：获取所有机场列表
 *   - getAirportById(id)：根据ID获取机场信息
 *   - getAirportByCity(city)：根据城市获取机场信息
 *   - containsAirportKeywords(text)：检查文本是否包含机场关键词
 *
 * 【依赖关系网络】
 * 直接依赖：无（基础设施数据）
 *
 * 被依赖关系：
 *   - 被config.js作为机场数据源依赖
 *   - 被addressTranslator用于地址转换
 *   - 被所有需要机场信息的模块间接依赖
 *   - 影响整个应用的地理位置处理准确性
 *
 * 【加载时机与生命周期】
 * 加载时机：HTML解析阶段同步加载（必须在config.js之前）
 * 初始化时机：文件执行时立即初始化全局变量
 * 生命周期：
 *   1. 文件加载 → 2. 数据结构初始化 → 3. 全局变量赋值 → 4. 运行时查询访问 → 5. 持续可用（无清理）
 * 清理处理：静态数据，无需清理
 *
 * 【技术栈与环境要求】
 * 运行环境：浏览器环境（依赖window对象）
 * 技术栈：原生JavaScript ES6+，无框架依赖
 * 浏览器支持：现代浏览器（支持const、箭头函数、正则表达式）
 * 数据要求：包含东南亚主要机场的完整信息
 *
 * 【核心功能说明】
 * 1. 机场信息映射：中英文名称、代码、地理位置的完整映射
 * 2. 关键词识别：机场相关内容的自动识别规则
 * 3. 城市映射：城市名称到机场标识符的快速查找
 * 4. 查询工具：提供各种维度的机场信息查询接口
 * 5. 全局访问：通过window对象提供应用-wide访问
 *
 * 【相关文件索引】
 * - config.js：使用本数据源作为机场数据提供者
 * - addressTranslator.js：使用机场信息进行地址转换
 * - 机场信息数据：东南亚主要机场的标准化数据
 *
 * 【修改记录】
 * v1.0.0 (2025-08-31) - 初始版本
 *   - 建立完整的东南亚机场数据结构
 *   - 实现中英文名称映射
 *   - 添加IATA/ICAO代码支持
 *   - 实现关键词识别功能
 *   - 添加详细的防AI幻觉注释系统
 *   - 提供查询工具函数
 *
 * 【使用说明】
 * 数据访问：通过window.AIRPORT_DATA_SOURCE访问全局数据对象
 * 查询方法：使用提供的工具函数进行各种查询操作
 * 数据扩展：新机场信息应按照现有结构添加到AIRPORT_MAPPINGS
 * 关键词更新：机场关键词应同步更新到AIRPORT_KEYWORDS
 *
 * ============================================================================
 */

'use strict';

// ==================== 机场数据配置 ====================

/**
 * 主要机场中英文名称映射表
 * @DATA_STRUCTURE 机场信息完整映射
 */
const AIRPORT_MAPPINGS = {
    // 马来西亚机场
    'kuala_lumpur': {
        chinese_names: ['吉隆坡机场', '吉隆坡国际机场', 'KLIA', '雪邦机场'],
        english_name: 'Kuala Lumpur International Airport',
        iata_code: 'KUL',
        icao_code: 'WMKK',
        city: 'Kuala Lumpur',
        country: 'Malaysia',
        region: '吉隆坡'
    },
    'kuala_lumpur_2': {
        chinese_names: ['吉隆坡第二国际机场', 'KLIA2', '廉价航空机场'],
        english_name: 'Kuala Lumpur International Airport 2',
        iata_code: 'KUL',
        icao_code: 'WMKK',
        city: 'Kuala Lumpur',
        country: 'Malaysia',
        region: '吉隆坡'
    },
    'penang': {
        chinese_names: ['槟城机场', '槟城国际机场', '槟岛机场'],
        english_name: 'Penang International Airport',
        iata_code: 'PEN',
        icao_code: 'WMKP',
        city: 'Penang',
        country: 'Malaysia',
        region: '槟城'
    },
    'johor_bahru': {
        chinese_names: ['新山机场', '柔佛机场', '士乃机场'],
        english_name: 'Johor Bahru Senai International Airport',
        iata_code: 'JHB',
        icao_code: 'WMKJ',
        city: 'Johor Bahru',
        country: 'Malaysia',
        region: '新山'
    },
    'kota_kinabalu': {
        chinese_names: ['亚庇机场', '哥打京那巴鲁机场', '沙巴机场'],
        english_name: 'Kota Kinabalu International Airport',
        iata_code: 'BKI',
        icao_code: 'WBKK',
        city: 'Kota Kinabalu',
        country: 'Malaysia',
        region: '亚庇'
    },
    'kuching': {
        chinese_names: ['古晋机场', '砂拉越机场'],
        english_name: 'Kuching International Airport',
        iata_code: 'KCH',
        icao_code: 'WBGG',
        city: 'Kuching',
        country: 'Malaysia',
        region: '古晋'
    },

    // 新加坡机场
    'singapore': {
        chinese_names: ['新加坡机场', '樟宜机场', '新加坡樟宜机场', '新加坡国际机场'],
        english_name: 'Singapore Changi Airport',
        iata_code: 'SIN',
        icao_code: 'WSSS',
        city: 'Singapore',
        country: 'Singapore',
        region: '新加坡'
    },

    // 泰国机场
    'bangkok_suvarnabhumi': {
        chinese_names: ['曼谷机场', '素万那普机场', '曼谷国际机场'],
        english_name: 'Suvarnabhumi International Airport',
        iata_code: 'BKK',
        icao_code: 'VTBS',
        city: 'Bangkok',
        country: 'Thailand',
        region: '曼谷'
    },
    'bangkok_don_mueang': {
        chinese_names: ['廊曼机场', '曼谷廊曼机场', '旧曼谷机场'],
        english_name: 'Don Mueang International Airport',
        iata_code: 'DMK',
        icao_code: 'VTBD',
        city: 'Bangkok',
        country: 'Thailand',
        region: '曼谷'
    },
    'phuket': {
        chinese_names: ['普吉机场', '普吉岛机场', '普吉国际机场'],
        english_name: 'Phuket International Airport',
        iata_code: 'HKT',
        icao_code: 'VTSP',
        city: 'Phuket',
        country: 'Thailand',
        region: '普吉'
    },

    // 印尼机场
    'jakarta': {
        chinese_names: ['雅加达机场', '苏加诺-哈达机场', '雅加达国际机场'],
        english_name: 'Soekarno-Hatta International Airport',
        iata_code: 'CGK',
        icao_code: 'WIII',
        city: 'Jakarta',
        country: 'Indonesia',
        region: '雅加达'
    },
    'bali': {
        chinese_names: ['巴厘岛机场', '登巴萨机场', '努拉莱伊机场'],
        english_name: 'Ngurah Rai International Airport',
        iata_code: 'DPS',
        icao_code: 'WADD',
        city: 'Denpasar',
        country: 'Indonesia',
        region: '巴厘岛'
    },

    // 越南机场
    'ho_chi_minh': {
        chinese_names: ['胡志明市机场', '新山一机场', '西贡机场'],
        english_name: 'Tan Son Nhat International Airport',
        iata_code: 'SGN',
        icao_code: 'VVTS',
        city: 'Ho Chi Minh City',
        country: 'Vietnam',
        region: '胡志明市'
    },
    'hanoi': {
        chinese_names: ['河内机场', '内排机场', '河内国际机场'],
        english_name: 'Noi Bai International Airport',
        iata_code: 'HAN',
        icao_code: 'VVNB',
        city: 'Hanoi',
        country: 'Vietnam',
        region: '河内'
    },

    // 菲律宾机场
    'manila': {
        chinese_names: ['马尼拉机场', '尼诺·阿基诺机场', '马尼拉国际机场'],
        english_name: 'Ninoy Aquino International Airport',
        iata_code: 'MNL',
        icao_code: 'RPLL',
        city: 'Manila',
        country: 'Philippines',
        region: '马尼拉'
    },

    // 中国主要机场
    'guangzhou': {
        chinese_names: ['广州机场', '白云机场', '广州白云机场'],
        english_name: 'Guangzhou Baiyun International Airport',
        iata_code: 'CAN',
        icao_code: 'ZGGG',
        city: 'Guangzhou',
        country: 'China',
        region: '广州'
    },
    'shenzhen': {
        chinese_names: ['深圳机场', '宝安机场', '深圳宝安机场'],
        english_name: 'Shenzhen Bao\'an International Airport',
        iata_code: 'SZX',
        icao_code: 'ZGSZ',
        city: 'Shenzhen',
        country: 'China',
        region: '深圳'
    }
};

/**
 * 机场关键词识别规则
 * @ENUMERATION 机场识别关键词分类
 */
const AIRPORT_KEYWORDS = {
    // 中文关键词
    chinese: [
        '机场', '国际机场', '航站楼', '候机楼', '机场大厅',
        '出发厅', '到达厅', '登机口', '航班', '起飞', '降落'
    ],
    // 英文关键词
    english: [
        'airport', 'international airport', 'terminal', 'departure',
        'arrival', 'gate', 'flight', 'takeoff', 'landing'
    ],
    // 机场代码模式
    codes: [
        /\b[A-Z]{3}\b/g,  // IATA代码 (3字母)
        /\b[A-Z]{4}\b/g   // ICAO代码 (4字母)
    ]
};

/**
 * 城市到机场的映射
 * @REFERENCE 城市名称到机场标识符的快速查找
 */
const CITY_TO_AIRPORT = {
    // 中文城市名
    '吉隆坡': 'kuala_lumpur',
    '槟城': 'penang',
    '新山': 'johor_bahru',
    '亚庇': 'kota_kinabalu',
    '古晋': 'kuching',
    '新加坡': 'singapore',
    '曼谷': 'bangkok_suvarnabhumi',
    '普吉': 'phuket',
    '雅加达': 'jakarta',
    '巴厘岛': 'bali',
    '胡志明市': 'ho_chi_minh',
    '河内': 'hanoi',
    '马尼拉': 'manila',
    '广州': 'guangzhou',
    '深圳': 'shenzhen',
    
    // 英文城市名
    'kuala lumpur': 'kuala_lumpur',
    'penang': 'penang',
    'johor bahru': 'johor_bahru',
    'kota kinabalu': 'kota_kinabalu',
    'kuching': 'kuching',
    'singapore': 'singapore',
    'bangkok': 'bangkok_suvarnabhumi',
    'phuket': 'phuket',
    'jakarta': 'jakarta',
    'bali': 'bali',
    'ho chi minh': 'ho_chi_minh',
    'hanoi': 'hanoi',
    'manila': 'manila',
    'guangzhou': 'guangzhou',
    'shenzhen': 'shenzhen'
};

// ==================== 导出数据对象 ====================

const AIRPORT_DATA_SOURCE = {
    mappings: AIRPORT_MAPPINGS,
    keywords: AIRPORT_KEYWORDS,
    cityToAirport: CITY_TO_AIRPORT,
    
    /**
     * 获取所有机场信息
     * @UTIL 获取完整机场数据列表
     */
    getAllAirports() {
        return Object.values(this.mappings);
    },
    
    /**
     * 根据机场标识符获取机场信息
     * @UTIL 机场信息查询工具
     */
    getAirportById(airportId) {
        return this.mappings[airportId] || null;
    },
    
    /**
     * 根据城市名获取机场信息
     * @UTIL 城市到机场的映射查询
     */
    getAirportByCity(cityName) {
        const airportId = this.cityToAirport[cityName.toLowerCase()];
        return airportId ? this.mappings[airportId] : null;
    },
    
    /**
     * 检查文本是否包含机场关键词
     * @UTIL 机场内容识别工具
     */
    containsAirportKeywords(text) {
        const lowerText = text.toLowerCase();
        
        // 检查中文关键词
        for (const keyword of this.keywords.chinese) {
            if (text.includes(keyword)) return true;
        }
        
        // 检查英文关键词
        for (const keyword of this.keywords.english) {
            if (lowerText.includes(keyword)) return true;
        }
        
        // 检查机场代码
        for (const pattern of this.keywords.codes) {
            // Resetting lastIndex for global regexes
            pattern.lastIndex = 0;
            if (pattern.test(text)) return true;
        }
        
        return false;
    }
};

// 暴露到全局变量
window.AIRPORT_DATA_SOURCE = AIRPORT_DATA_SOURCE;
