# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个渠道检测编辑器项目，用于自动检测和处理各种旅游平台的订单信息。该项目是一个独立的Web应用，可在浏览器中运行，支持多渠道订单内容解析、字段映射和规则管理。

## 常用命令

### 直接运行（纯静态项目）
```bash
# 直接在浏览器中打开主文件
open channel-detection-editor/index.html
# 或双击 index.html 文件
```

### 可选构建优化（如需要）
```bash
# 仅用于文件合并和压缩优化，非必需
node channel-detection-editor/build.js
# 然后使用优化版本：channel-detection-editor/index-optimized.html
```

### 测试
- 主应用：`channel-detection-editor/index.html`
- 综合测试：`tests/integration/comprehensive-test-suite.html`
- 自动化测试：`tests/integration/automated-test-runner.html`
- 性能测试：`tests/performance/cache-performance-test.html`

### 开发工作流快捷键（页面内）
- `Ctrl+Shift+T` - 快速开发验证流程
- `Ctrl+Shift+D` - 开发工作流面板
- `Ctrl+Shift+P` - 性能基准测试

## 核心架构

### 模块化架构（依赖注入）
- **module-container.js** - 核心依赖注入容器，管理所有模块实例
- **app.js** - 主应用程序，通过容器获取服务
- **config.js** - 配置管理器，处理系统配置和数据源

### 核心功能模块
- **channel-detector.js** - 渠道检测器，基于正则和模式匹配
- **field-mapper.js** - 字段映射器，从文本提取订单信息
- **rule-editor.js** - 规则编辑器，可视化管理检测规则
- **prompt-editor.js** - 提示词编辑器，集成AI优化功能

### 缓存系统
- **cache-manager.js** - 统一缓存管理，支持TTL和LRU策略
- **cache-integration-adapter.js** - 缓存集成适配器，零侵入代理模式
- **cache-monitor-panel.js** - 缓存性能监控面板

### AI集成
- **gemini-config.js** - Gemini API配置管理
- **prompt-composer.js** - 智能提示词组装器
- **prompt-segmenter.js** - 提示词分段管理
- **prompt-fragments.js** - 提示词片段统一处理

### 工作流优化
- **workflow-dev-optimizer.js** - 开发工作流优化
- **user-workflow-enhancer.js** - 用户工作流增强
- **maintenance-workflow-manager.js** - 维护工作流管理

## 文件结构和依赖顺序

### 构建文件加载顺序（关键）
构建脚本 `build.js` 中定义的文件加载顺序必须严格遵循：
1. config.js - 配置优先
2. gemini-config.js - API配置
3. data.js - 数据文件
4. channel-detector.js - 核心检测
5. field-mapper.js - 字段映射
6. 其他工具和编辑器模块
7. app.js - 主应用（最后）

### 数据层
- **data.js** - 统一数据源（车型、区域、语言配置）
- **hotels_by_region.js** - 酒店数据库
- **airport-data.js** - 机场数据库

## 技术特性

### 渠道支持
- **Fliggy（飞猪）** - 19位订单编号模式检测
- **JingGe** - 关键词匹配
- **KKday/Klook** - 参考号前缀检测
- **Ctrip** - 多模式检测

### AI功能
- 使用 Gemini-2.5-Flash 进行智能文本处理
- 使用 Gemini-2.5-Pro 进行提示词优化
- 支持批量AI优化和版本对比

### 性能优化
- 智能缓存系统可提升 60-95% 性能
- 构建系统可减少约 60-70% 文件大小
- 单文件部署减少 HTTP 请求

## 开发注意事项

### 纯静态项目特性
- **无第三方依赖**：所有功能都用原生JavaScript实现
- **直接运行**：只需在浏览器中打开index.html即可使用
- **模块加载**：通过script标签按顺序加载所有模块文件

### 文件加载顺序（重要）
HTML中的脚本加载顺序必须严格遵循：
1. module-container.js（依赖注入容器，必须最先）
2. 核心服务和工具模块
3. 配置和数据模块
4. 业务逻辑模块
5. UI/编辑器模块
6. 缓存系统
7. app.js（主应用逻辑）
8. main.js（统一初始化器，必须最后）

### 模块依赖
- 所有模块通过依赖注入容器管理
- 使用 `container.get(serviceName)` 获取服务实例
- 避免直接创建模块实例

### 缓存使用
- 缓存键使用统一命名规范
- 支持手动清理和自动TTL过期
- 监控缓存性能和命中率

### API配置
- Gemini API密钥已硬编码到配置中
- 所有配置通过config.js管理
- 支持多种AI模型切换

### 构建系统（可选）
- build.js仅用于文件合并和压缩优化
- 需要Node.js环境和terser包（`npm install terser`）
- 生成的优化文件可提升加载性能，但不是必需的