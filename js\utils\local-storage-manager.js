/**
 * 【全局暴露与模块容器冲突记录 - 2025-01-01】
 * 
 * 问题：
 * - getStats()方法在数据未初始化时调用会出现null引用错误
 *   错误信息："Cannot read properties of null (reading 'channels')"
 *   发生位置：第469行 Object.keys(this.data.channels)
 * - showStorageStats()函数（第594行）期望window.localStorageManager存在
 * - 模块容器系统不会自动将模块实例暴露到window对象
 * 
 * 根源分析：
 * 1. 数据初始化时机：constructor中this.data设为null，异步初始化后才有值
 * 2. 调用时机：setTimeout在第594行调用getStats()可能在初始化完成前执行
 * 3. 暴露机制：模块容器封装实例，不自动暴露到全局window对象
 * 4. 兼容性问题：旧代码期望直接访问window.localStorageManager
 * 
 * 当前修复：
 * - 在getStats()中添加了null检查防护（第469-477行）
 * - 返回默认值避免运行时错误
 * 
 * 影响范围：
 * - 统计功能可能无法正常工作
 * - 任何期望window.localStorageManager的代码会失败
 * - 模块容器外的代码无法访问实例
 * 
 * 需要考虑的解决方案：
 * 1. 在初始化完成后暴露到window对象
 * 2. 修改调用方式使用container.get('localStorageManager')
 * 3. 提供兼容性包装函数
 * 4. 确保初始化完成后再调用相关函数
 * 
 * ============================================================================
 * 
 * 本地存储管理模块 (local-storage-manager.js) - 防AI幻觉核心声明
 * ============================================================================
 *
 * 【防AI幻觉声明】
 * 本文件是完整的本地持久化解决方案，负责所有数据的本地存储和管理。
 * 严禁AI基于文件名推测功能，必须完整阅读所有存储逻辑和加密机制才能理解数据持久化的完整流程。
 * 本文件不直接处理业务逻辑，仅提供数据存储和检索服务。
 *
 * 【重复开发防护】
 * 警告：本项目已存在完整的本地存储管理系统，严禁创建新的数据持久化模块。
 * 任何新的数据存储需求必须通过本管理器实现，而不是直接操作localStorage。
 * 现有存储模式：[数据分类 → 加密处理 → 序列化存储 → 检索反序列化] - 严禁修改此约定。
 *
 * 【竞态问题防护】
 * 数据存储竞态防护：
 * 1. 异步初始化序列化：initializeAsync方法确保初始化完成后再接受操作
 * 2. 数据保存原子化：saveAllData方法中的数据更新和存储序列化
 * 3. 加密操作保护：cryptoUtils的异步加密/解密操作按顺序执行
 * 4. 历史记录同步：addHistory方法中的数组操作原子化
 * 防护措施：使用async/await序列化、数据克隆、操作队列确保存储的线程安全。
 *
 * 【声明与接口】
 * 导出：LocalStorageManager类、createLocalStorageManagerModule工厂函数
 * 导入：依赖cryptoUtils模块容器服务（可选）
 * 数据结构：
 *   - data.channels：渠道数据映射
 *   - data.rules：规则数据映射
 *   - data.prompts：提示词数据映射
 *   - data.history：操作历史记录数组
 * 主要接口：
 *   - loadData(key)：异步加载指定键的数据
 *   - saveData(key, value)：异步保存键值对数据
 *   - saveChannel/channel/rule/prompt()：保存各类业务数据
 *   - exportData()：导出所有数据
 *   - importData(data)：导入数据
 *   - searchContent(query)：搜索存储内容
 *
 * 【依赖关系网络】
 * 直接依赖：
 *   - cryptoUtils：加密工具（可选，用于安全存储）
 *   - localStorage：浏览器本地存储API
 *
 * 被依赖关系：
 *   - 被main.js注册为本地存储管理模块
 *   - 被geminiConfig依赖存储API密钥
 *   - 被ruleEditor依赖存储渠道规则
 *   - 被promptEditor依赖存储提示词模板
 *   - 被所有需要数据持久化的模块间接依赖
 *   - 影响整个应用的数据完整性和用户体验
 *
 * 【加载时机与生命周期】
 * 加载时机：模块容器初始化阶段按依赖顺序加载
 * 初始化时机：构造函数中开始异步初始化，initializeAsync完成后再可用
 * 生命周期：
 *   1. 文件加载 → 2. 工厂函数注册 → 3. 依赖注入 → 4. 异步初始化开始 → 5. 数据结构检查 → 6. 运行时数据操作 → 7. 定期保存 → 8. 清理销毁
 * 清理处理：clearAllData方法清理所有数据
 *
 * 【技术栈与环境要求】
 * 运行环境：浏览器环境（依赖localStorage和window对象）
 * 技术栈：原生JavaScript ES6+，无框架依赖
 * 浏览器支持：现代浏览器（支持async/await、Map、localStorage）
 * 安全要求：支持加密存储（需要cryptoUtils模块）
 *
 * 【核心功能说明】
 * 1. 统一数据持久化：所有应用数据的本地存储管理
 * 2. 加密存储支持：可选的AES加密保护敏感数据
 * 3. 数据分类管理：渠道、规则、提示词、历史的分类存储
 * 4. 操作历史记录：完整的用户操作历史追踪
 * 5. 数据导入导出：支持数据备份和迁移
 * 6. 搜索功能：全文搜索存储的内容
 * 7. 统计信息：存储使用情况和数据统计
 * 8. 向后兼容：支持全局实例和模块容器两种模式
 *
 * 【相关文件索引】
 * - main.js：负责本模块的注册
 * - gemini-config.js：使用本管理器存储API密钥
 * - rule-editor.js：使用本管理器存储渠道规则
 * - prompt-editor.js：使用本管理器存储提示词模板
 * - crypto-utils.js：提供加密功能支持
 *
 * 【修改记录】
 * v1.0.0 (2025-08-31) - 重构版本
 *   - 实现完整的本地存储管理架构
 *   - 添加加密存储和降级机制
 *   - 完善数据分类和历史记录
 *   - 添加详细的防AI幻觉注释系统
 *   - 实现数据导入导出功能
 *   - 优化异步操作和错误处理
 *
 * 【使用说明】
 * 数据存储：使用saveData方法保存任意键值对数据
 * 数据加载：使用loadData方法异步加载指定数据
 * 业务数据：使用专用方法保存渠道/规则/提示词数据
 * 数据导出：使用exportData方法备份所有数据
 * 数据导入：使用importData方法恢复备份数据
 * 搜索查询：使用searchContent方法搜索存储内容
 *
 * ============================================================================
 */

/**
 * 本地存储管理模块 - 完整的本地持久化解决方案
 *
 * === 文件依赖关系网络 ===
 * 依赖项：crypto-utils.js（可选加密功能）
 * 被依赖：rule-editor.js, prompt-editor.js（数据持久化）
 * 全局变量：创建 window.storageManager 实例
 * 存储管理：渠道规则、提示词片段、历史记录、配置数据
 *
 * === 核心功能 ===
 * - 统一的本地数据持久化管理
 * - 分类数据存储（渠道、规则、提示词、历史）
 * - 可选加密存储（依赖crypto-utils）
 * - 数据备份和恢复功能
 * - 存储空间管理和清理
 *
 * === 集成点 ===
 * - rule-editor.js：保存和加载渠道检测规则
 * - prompt-editor.js：保存和加载提示词模板
 * - app.js：历史记录和用户偏好存储
 * - crypto-utils.js：可选的安全存储功能
 *
 * === 使用场景 ===
 * - 渠道检测规则的持久化存储
 * - 提示词模板的编辑和保存
 * - 用户操作历史的记录和查看
 * - 系统配置的本地缓存
 *
 * === 注意事项 ===
 * 支持加密和明文两种存储模式，自动降级
 * 包含数据结构检查和修复机制
 * 支持数据导入导出和批量操作
 * 使用异步初始化支持加密操作
 */

// 本地存储管理器 - 完整的本地持久化解决方案

class LocalStorageManager {
  constructor(cryptoUtils) {
    this.cryptoUtils = cryptoUtils;
    this.storageKey = 'channel_detection_editor_data';
    this.data = null;
    this._initialized = false;
    // ✅ 移除 this.initializeAsync() 调用，由容器控制初始化时机
  }

  async initialize() {
    if (this._initialized) {
      console.log('⚠️ 本地存储管理器已初始化，跳过重复初始化');
      return;
    }
    
    try {
      this.data = await this.loadAllData();
      this.ensureDataStructure();
      this._initialized = true;
      console.log('本地存储管理器已初始化 (Refactored)');
    } catch (error) {
      console.error('❌ 本地存储初始化失败:', error);
      // 使用默认数据结构
      this.data = this.getDefaultDataStructure();
      this._initialized = true;
      throw error;
    }
  }

  getDefaultDataStructure() {
    return {
      channels: {},
      rules: {},
      prompts: {},
      history: {},
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * 确保数据结构完整
   */
  ensureDataStructure() {
    if (!this.data.channels) this.data.channels = {};
    if (!this.data.rules) this.data.rules = {};
    if (!this.data.prompts) this.data.prompts = {};
    if (!this.data.history) this.data.history = [];

    this.saveAllData();
  }

  /**
   * 加载所有数据
   */
  async loadAllData() {
    try {
      // 优先尝试加密读取
      if (this.cryptoUtils) {
        const encryptedData = await this.cryptoUtils.secureGetItem(this.storageKey);
        if (encryptedData) {
          return encryptedData;
        }
      }

      // 降级到普通读取
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : this.createDefaultData();

    } catch (error) {
      console.error('加载本地数据失败:', error);
      return this.createDefaultData();
    }
  }

  /**
   * 创建默认数据
   */
  createDefaultData() {
    return {
      channels: {},
      rules: {},
      prompts: {},
      history: [],
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * 保存所有数据
   */
  async saveAllData() {
    try {
      this.data.lastUpdated = new Date().toISOString();

      // 优先尝试加密保存
      if (this.cryptoUtils) {
        const success = await this.cryptoUtils.secureSetItem(this.storageKey, this.data);
        if (success) {
          return;
        }
      }

      // 降级到普通保存
      localStorage.setItem(this.storageKey, JSON.stringify(this.data));

    } catch (error) {
      console.error('保存数据失败:', error);
    }
  }

  /**
   * 通用键值读写接口（供其他模块使用，如 gemini-config）
   * @SERVICE 统一KV存取
   */
  async loadData(key) {
    try {
      await this.initializeAsync?.(); // 确保已初始化（幂等） @LIFECYCLE
    } catch {}
    const all = await this.loadAllData();
    return all ? all[key] : null;
  }

  async saveData(key, value) {
    try {
      await this.initializeAsync?.(); // 确保已初始化（幂等） @LIFECYCLE
      if (!this.data) this.data = this.createDefaultData();
      this.data[key] = value;
      await this.saveAllData();
      return true;
    } catch (error) {
      console.error('保存数据失败:', error);
      return false;
    }
  }

  /**
   * 渠道管理
   */
  getAllChannels() {
    return Object.values(this.data.channels);
  }

  getChannel(channelKey) {
    return this.data.channels[channelKey];
  }

  saveChannel(channelKey, channelData) {
    this.data.channels[channelKey] = {
      ...channelData,
      _id: channelKey,
      updatedAt: new Date().toISOString(),
      createdAt: this.data.channels[channelKey]?.createdAt || new Date().toISOString()
    };

    this.addHistory('channel', channelKey, 'save', channelData);
    this.saveAllData();
    return this.data.channels[channelKey];
  }

  deleteChannel(channelKey) {
    const deleted = this.data.channels[channelKey];
    if (deleted) {
      delete this.data.channels[channelKey];
      this.addHistory('channel', channelKey, 'delete', deleted);
      this.saveAllData();
    }
    return deleted;
  }

  /**
   * 规则管理
   */
  getAllRules() {
    return Object.values(this.data.rules);
  }

  /**
   * 批量保存规则
   */
  saveAllRules(rules) {
    Object.entries(rules).forEach(([ruleKey, ruleData]) => {
      this.saveRule(ruleKey, ruleData);
    });

    this.addHistory('rule', 'all', 'batch_save', { count: Object.keys(rules).length });
    return true;
  }

  getRule(ruleKey) {
    return this.data.rules[ruleKey];
  }

  saveRule(ruleKey, ruleData) {
    this.data.rules[ruleKey] = {
      ...ruleData,
      _id: ruleKey,
      updatedAt: new Date().toISOString(),
      createdAt: this.data.rules[ruleKey]?.createdAt || new Date().toISOString()
    };

    this.addHistory('rule', ruleKey, 'save', ruleData);
    this.saveAllData();
    return this.data.rules[ruleKey];
  }

  deleteRule(ruleKey) {
    const deleted = this.data.rules[ruleKey];
    if (deleted) {
      delete this.data.rules[ruleKey];
      this.addHistory('rule', ruleKey, 'delete', deleted);
      this.saveAllData();
    }
    return deleted;
  }

  /**
   * 提示词管理
   */
  getAllPrompts() {
    return this.data.prompts;
  }

  getPrompt(channel, field) {
    return this.data.prompts[channel]?.[field];
  }

  savePrompt(channel, field, content) {
    if (!this.data.prompts[channel]) {
      this.data.prompts[channel] = {};
    }

    this.data.prompts[channel][field] = {
      content: content,
      field: field,
      channel: channel,
      updatedAt: new Date().toISOString(),
      createdAt: this.data.prompts[channel]?.[field]?.createdAt || new Date().toISOString()
    };

    this.addHistory('prompt', `${channel}_${field}`, 'save', { content });
    this.saveAllData();
    return this.data.prompts[channel][field];
  }

  deletePrompt(channel, field) {
    const deleted = this.data.prompts[channel]?.[field];
    if (deleted) {
      delete this.data.prompts[channel][field];
      this.addHistory('prompt', `${channel}_${field}`, 'delete', deleted);
      this.saveAllData();
    }
    return deleted;
  }

  /**
   * 变更历史
   */
  addHistory(entityType, entityId, action, data) {
    this.data.history.unshift({
      _id: Date.now().toString(),
      entityType: entityType,
      entityId: entityId,
      action: action,
      data: data,
      timestamp: new Date().toISOString()
    });

    // 保持最近100条历史记录
    if (this.data.history.length > 100) {
      this.data.history = this.data.history.slice(0, 100);
    }
  }

  getHistory(entityType = null, entityId = null) {
    let history = this.data.history;

    if (entityType) {
      history = history.filter(item => item.entityType === entityType);
    }

    if (entityId) {
      history = history.filter(item => item.entityId === entityId);
    }

    return history.slice(0, 50); // 返回最近50条
  }

  /**
   * 导出数据
   */
  exportData() {
    return {
      ...this.data,
      exportDate: new Date().toISOString(),
      version: '1.0'
    };
  }

  /**
   * 导入数据
   */
  importData(importedData) {
    if (importedData && typeof importedData === 'object') {
      this.data = {
        ...this.createDefaultData(),
        ...importedData,
        lastUpdated: new Date().toISOString()
      };
      this.saveAllData();
      this.addHistory('system', 'all', 'import', { source: 'external' });
      return true;
    }
    return false;
  }

  /**
   * 清空所有数据
   */
  clearAllData() {
    this.data = this.createDefaultData();
    this.saveAllData();
    this.addHistory('system', 'all', 'clear', {});
  }

  /**
   * 获取统计信息
   */
  getStats() {
    // 防护：确保data已初始化
    if (!this.data) {
      return {
        channels: 0,
        rules: 0,
        prompts: 0,
        history: 0,
        lastUpdated: null,
        status: 'not_initialized'
      };
    }
    
    return {
      channels: Object.keys(this.data.channels || {}).length,
      rules: Object.keys(this.data.rules || {}).length,
      prompts: Object.keys(this.data.prompts || {}).reduce((count, channel) =>
        count + Object.keys(this.data.prompts[channel] || {}).length, 0),
      history: (this.data.history || []).length,
      lastUpdated: this.data.lastUpdated,
      storageSize: JSON.stringify(this.data).length
    };
  }

  /**
   * 搜索功能
   */
  searchContent(query) {
    const results = [];
    const searchText = query.toLowerCase();

    // 搜索渠道
    Object.entries(this.data.channels).forEach(([key, channel]) => {
      if (JSON.stringify(channel).toLowerCase().includes(searchText)) {
        results.push({ type: 'channel', id: key, data: channel });
      }
    });

    // 搜索规则
    Object.entries(this.data.rules).forEach(([key, rule]) => {
      if (JSON.stringify(rule).toLowerCase().includes(searchText)) {
        results.push({ type: 'rule', id: key, data: rule });
      }
    });

    // 搜索提示词
    Object.entries(this.data.prompts).forEach(([channel, fields]) => {
      Object.entries(fields).forEach(([field, prompt]) => {
        if (prompt.content.toLowerCase().includes(searchText)) {
          results.push({ type: 'prompt', id: `${channel}_${field}`, data: prompt });
        }
      });
    });

    return results;
  }
}

// 模块工厂：供DI容器使用，优先复用全局实例 @FACTORY
function createLocalStorageManagerModule(container) {
  try {
    if (window.localStorageManager instanceof LocalStorageManager) {
      return window.localStorageManager; // 复用已存在实例，避免重复初始化 @LIFECYCLE
    }
  } catch {}
  let cryptoUtils = null;
  try {
    if (container && typeof container.get === 'function') {
      cryptoUtils = container.get('cryptoUtils'); // 可选依赖 @DEPENDENCY
    }
  } catch {}
  const instance = new LocalStorageManager(cryptoUtils);
  window.localStorageManager = instance; // 回填全局以保持向后兼容 @LIFECYCLE
  return instance;
}

// 注册到模块容器（保持一致的日志与可用性） @LIFECYCLE
if (typeof window !== 'undefined' && window.registerModule) {
  window.registerModule('localStorageManager', createLocalStorageManagerModule, ['cryptoUtils']);
  console.log('📦 LocalStorageManager 已注册到模块容器');
}


// 创建全局实例（若未通过工厂创建）
if (!(window.localStorageManager instanceof LocalStorageManager)) {
  window.localStorageManager = new LocalStorageManager();
}

// 工具函数
function exportEditorData() {
  const data = window.localStorageManager.exportData();
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'channel-editor-backup.json';
  a.click();
  URL.revokeObjectURL(url);
}

function importEditorData(file) {
  const reader = new FileReader();
  reader.onload = function(e) {
    try {
      const data = JSON.parse(e.target.result);
      if (window.localStorageManager.importData(data)) {
        alert('数据导入成功！');
        location.reload();
      } else {
        alert('数据导入失败：格式错误');
      }
    } catch (error) {
      alert('数据导入失败：' + error.message);
    }
  };
  reader.readAsText(file);
}

function clearEditorData() {
  if (confirm('确定要清空所有数据吗？此操作不可恢复！')) {
    window.localStorageManager.clearAllData();
    alert('数据已清空');
    location.reload();
  }
}

function showStorageStats() {
  const stats = window.localStorageManager.getStats();
  alert(`数据统计：
渠道: ${stats.channels} 个
规则: ${stats.rules} 条
提示词: ${stats.prompts} 个
历史记录: ${stats.history} 条
最后更新: ${new Date(stats.lastUpdated).toLocaleString()}
存储大小: ${Math.round(stats.storageSize / 1024)} KB`);
}

// 初始化完成后显示状态
document.addEventListener('DOMContentLoaded', function() {
  setTimeout(() => {
    const stats = window.localStorageManager.getStats();
    console.log('本地存储初始化完成', stats);
  }, 1000);
});