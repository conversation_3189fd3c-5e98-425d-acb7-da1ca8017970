<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>阶段2验证 - 集中注册测试</title>
    <style>
        .test-result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 4px; 
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>🧪 阶段2验证：集中注册统一架构</h1>
    <div id="results"></div>

    <!-- 按照index.html的顺序加载模块 -->
    <script src="js/core/module-container.js"></script>
    <script src="js/core/error-handler.js"></script>
    <script src="js/utils/local-storage-manager.js"></script>
    <script src="js/utils/crypto-utils.js"></script>
    <script src="data/hotels_by_region.js"></script>
    <script src="js/config/airport-data.js"></script>
    <script src="js/config/config.js"></script>
    <script src="js/services/gemini-config.js"></script>
    <script src="js/business/channel-detector.js"></script>
    <script src="js/services/address-translator.js"></script>
    <script src="js/business/channel-data-manager.js"></script>
    <script src="js/business/prompt-segmenter.js"></script>
    <script src="js/business/prompt-composer.js"></script>
    <script src="js/business/prompt-fragments.js"></script>
    <script src="js/business/field-mapper.js"></script>
    <script src="js/business/prompt-processor.js"></script>
    <script src="js/core/main-modal-coordinator.js"></script>
    <script src="js/ui/prompt-manage-modal.js"></script>
    <script src="js/core/main.js"></script>

    <script>
        function addResult(message, type) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }

        function validatePhase2() {
            addResult('🚀 开始阶段2验证...', 'info');

            // 等待主应用初始化完成
            setTimeout(() => {
                runTests();
            }, 5000);  // 给更多时间让main.js初始化完成
        }

        function runTests() {
            const tests = [
                {
                    name: '模块容器正常工作',
                    test: () => window.moduleContainer && typeof window.moduleContainer.get === 'function'
                },
                {
                    name: 'promptProcessor通过容器获取',
                    test: () => {
                        try {
                            const processor = window.moduleContainer.get('promptProcessor');
                            return processor && typeof processor.processOrderContent === 'function';
                        } catch (e) {
                            console.error('promptProcessor获取失败:', e);
                            return false;
                        }
                    }
                },
                {
                    name: 'promptManageModal通过容器获取',
                    test: () => {
                        try {
                            const modal = window.moduleContainer.get('promptManageModal');
                            return modal && typeof modal.openModal === 'function';
                        } catch (e) {
                            console.error('promptManageModal获取失败:', e);
                            return false;
                        }
                    }
                },
                {
                    name: 'mainModalCoordinator通过容器获取',
                    test: () => {
                        try {
                            const coordinator = window.moduleContainer.get('mainModalCoordinator');
                            return coordinator && typeof coordinator.linkComponents === 'function';
                        } catch (e) {
                            console.error('mainModalCoordinator获取失败:', e);
                            return false;
                        }
                    }
                },
                {
                    name: '全局暴露兼容性 - window.localStorageManager',
                    test: () => window.localStorageManager && typeof window.localStorageManager.getStats === 'function'
                },
                {
                    name: '全局暴露兼容性 - window.promptProcessor',
                    test: () => window.promptProcessor && typeof window.promptProcessor.processOrderContent === 'function'
                },
                {
                    name: '全局暴露兼容性 - window.promptManageModal',
                    test: () => window.promptManageModal && typeof window.promptManageModal.openModal === 'function'
                },
                {
                    name: '全局暴露兼容性 - window.mainModalCoordinator',
                    test: () => window.mainModalCoordinator && typeof window.mainModalCoordinator.linkComponents === 'function'
                },
                {
                    name: '重复注册检测正常工作',
                    test: () => {
                        // 这个通过控制台警告来验证，如果没有错误就说明工作正常
                        return true;
                    }
                },
                {
                    name: '协同组件正常初始化',
                    test: () => {
                        try {
                            const processor = window.promptProcessor;
                            const modal = window.promptManageModal;
                            const coordinator = window.mainModalCoordinator;
                            
                            return processor && modal && coordinator;
                        } catch (e) {
                            return false;
                        }
                    }
                }
            ];

            let passedTests = 0;
            tests.forEach(test => {
                try {
                    const passed = test.test();
                    if (passed) {
                        addResult(`✅ ${test.name}`, 'success');
                        passedTests++;
                    } else {
                        addResult(`❌ ${test.name} - 测试失败`, 'error');
                    }
                } catch (error) {
                    addResult(`❌ ${test.name} - 错误: ${error.message}`, 'error');
                }
            });

            // 检查控制台是否有初始化错误
            addResult('📋 请检查控制台是否有"未找到模块"错误', 'info');
            addResult('📋 如果看到"⚠️ 模块 xxx 已存在，跳过重复注册"说明防重复机制工作正常', 'info');

            // 总结
            addResult(`📊 验证完成: ${passedTests}/${tests.length} 项测试通过`, 
                passedTests === tests.length ? 'success' : 'error');

            if (passedTests === tests.length) {
                addResult('🎉 阶段2验证成功！集中注册和兼容性暴露正常工作', 'success');
                addResult('📋 检查要点:', 'info');
                addResult('  • 控制台应该没有"未找到模块"错误', 'info');
                addResult('  • 所有模块都可以通过容器和全局访问', 'info'); 
                addResult('  • 重复注册被正确跳过', 'info');
            } else {
                addResult('⚠️ 阶段2验证失败，需要检查问题', 'error');
            }
        }

        // 开始验证
        validatePhase2();
    </script>
</body>
</html>