/**
 * ============================================================================
 * 错误处理模块 (error-handler.js) - 防AI幻觉核心声明
 * ============================================================================
 *
 * 【防AI幻觉声明】
 * 本文件是全局错误处理和降级方案的统一管理中心，负责所有模块的错误捕获和用户反馈。
 * 严禁AI基于文件名推测功能，必须完整阅读所有错误分类、降级处理和安全执行逻辑才能理解错误处理机制的完整性。
 * 本文件不直接处理业务逻辑，仅提供错误处理和降级服务。
 *
 * 【重复开发防护】
 * 警告：本项目已存在完整的错误处理系统，严禁创建新的错误处理模块。
 * 任何新的错误处理需求必须通过本工具实现，而不是创建分散的错误处理逻辑。
 * 现有错误处理模式：[全局捕获 → 错误分类 → 降级处理 → 用户通知 → 日志记录] - 严禁修改此约定。
 *
 * 【竞态问题防护】
 * 错误处理竞态防护：
 * 1. 重入锁保护：handleError方法中的_handling标志防止递归调用
 * 2. 节流机制：console.error重写中的_lastLog状态防止刷屏
 * 3. 异步安全执行：safeExecuteAsync方法中的try/catch序列化错误处理
 * 4. 降级方案同步：withFallback方法中的主操作和降级操作按顺序执行
 * 防护措施：使用重入锁、节流计数器、原子性检查确保错误处理的可靠性。
 *
 * 【声明与接口】
 * 导出：ErrorHandler类、createErrorHandlerModule工厂函数
 * 导入：依赖window.addEventListener、localStorage、console
 * 主要接口：
 *   - handleError(errorInfo)：统一错误处理入口
 *   - safeExecute(fn, fallbackValue, context)：安全执行同步函数
 *   - safeExecuteAsync(fn, fallbackValue, context)：安全执行异步函数
 *   - withFallback(mainAction, fallbackAction, context)：降级处理机制
 *   - showUserError(title, message)：显示用户错误通知
 *   - showUserWarning(title, message)：显示用户警告通知
 *   - showUserInfo(title, message)：显示用户信息通知
 *   - getErrorLogs()：获取错误日志记录
 *   - clearErrorLogs()：清空错误日志
 *
 * 【依赖关系网络】
 * 直接依赖：
 *   - window.addEventListener：浏览器事件监听API @REQUIRED
 *   - localStorage：浏览器本地存储API @REQUIRED
 *   - console：浏览器控制台API @REQUIRED
 *   - document：DOM操作API @REQUIRED
 *
 * 被依赖关系：
 *   - 被main.js注册为全局错误处理模块
 *   - 被所有业务模块间接依赖用于错误处理
 *   - 被gemini-config.js依赖用于API调用失败降级
 *   - 被field-mapper.js依赖用于字段处理错误降级
 *   - 被local-storage-manager.js依赖用于存储失败降级
 *   - 影响整个应用的用户体验和错误反馈机制
 *
 * 【加载时机与生命周期】
 * 加载时机：模块容器初始化阶段优先加载（确保全局错误能被捕获）
 * 初始化时机：构造函数中立即设置全局错误监听器
 * 生命周期：
 *   1. 文件加载 → 2. 工厂函数注册 → 3. 实例创建 → 4. 全局监听器设置 → 5. 运行时错误捕获 → 6. 错误分类处理 → 7. 用户通知显示 → 8. 日志记录 → 9. 实例销毁
 * 清理处理：clearErrorLogs方法清理错误日志记录
 *
 * 【技术栈与环境要求】
 * 运行环境：浏览器环境（依赖DOM API和事件监听器）
 * 技术栈：原生JavaScript ES6+，无框架依赖
 * 浏览器支持：现代浏览器（支持addEventListener、localStorage、async/await）
 * 错误处理：支持多种错误类型（global、promise、console、custom）
 *
 * 【核心功能说明】
 * 1. 全局错误捕获：监听window.error和unhandledrejection事件
 * 2. 错误分类处理：根据错误类型采取不同处理策略
 * 3. 用户友好通知：提供视觉反馈和自动消失机制
 * 4. 安全执行包装：safeExecute系列方法防止代码崩溃
 * 5. 降级处理机制：withFallback方法提供备用方案
 * 6. 错误日志记录：完整的错误信息存储和检索
 * 7. 节流和重入保护：防止错误处理过程中的递归和刷屏
 *
 * 【相关文件索引】
 * - main.js：负责本模块的注册和初始化顺序
 * - gemini-config.js：使用降级处理进行API调用失败处理
 * - field-mapper.js：使用降级处理进行字段映射失败处理
 * - local-storage-manager.js：使用降级处理进行存储失败处理
 * - 所有业务模块：间接使用全局错误处理服务
 *
 * 【修改记录】
 * v1.0.0 (2025-08-31) - 初始版本
 *   - 实现全局错误捕获和分类处理
 *   - 添加用户友好的错误通知系统
 *   - 完善安全执行包装器和降级机制
 *   - 添加详细的防AI幻觉注释系统
 *   - 实现错误日志记录和检索功能
 *
 * 【使用说明】
 * 全局错误捕获：自动监听所有未处理的错误
 * 安全执行：使用safeExecute包装可能出错的代码
 * 降级处理：使用withFallback提供主操作和备用方案
 * 用户通知：使用showUserError/Warning/Info显示反馈
 * 日志管理：使用getErrorLogs和clearErrorLogs管理错误记录
 * 自定义错误：调用handleError方法处理自定义错误信息
 *
 * ============================================================================
 */

/**
 * 错误处理模块 - 全局错误捕获和降级方案
 *
 * === 文件依赖关系网络 ===
 * 依赖项：无直接依赖（使用浏览器原生事件监听）
 * 被依赖：所有模块（全局错误处理）
 * 全局变量：创建 window.errorHandler 实例和全局工具函数
 * 监听类型：global error, unhandledrejection, console.error
 *
 * === 核心功能 ===
 * - 全局错误捕获和分类处理
 * - 用户友好的错误通知显示
 * - 错误日志记录和存储
 * - 安全执行包装器（safeExecute）
 * - 降级处理机制（withFallback）
 *
 * === 集成点 ===
 * - 为所有模块提供全局错误处理服务
 * - gemini-config.js: API调用失败时显示降级提示
 * - field-mapper.js: 字段处理错误降级到本地模式
 * - local-storage-manager.js: 存储失败降级处理
 *
 * === 使用场景 ===
 * - 全局错误监控和统一处理
 * - API调用失败的降级方案
 * - 用户友好的错误反馈
 * - 开发调试和日志记录
 *
 * === 注意事项 ===
 * 该模块应在所有其他模块之前加载，确保全局错误能被捕获
 * 错误日志会保存到localStorage，最多100条记录
 * 支持自定义通知样式和自动消失
 */

// 错误处理和降级方案

class ErrorHandler {
    constructor() {
        this._initialized = false;
        // ✅ 移除 this.initialize() 调用，由容器控制初始化时机
    }

    initialize() {
        if (this._initialized) {
            console.log('⚠️ 错误处理器已初始化，跳过重复初始化');
            return;
        }
        
        console.log('错误处理器已初始化');
        // 重入锁与节流状态，防止递归与刷屏 @LIFECYCLE
        this._handling = false; // 重入锁
        this._lastLog = { msg: '', ts: 0, count: 0 }; // 简易节流（相同消息短时间内合并）

        // 全局错误捕获
        this.setupGlobalErrorHandling();

        // 未处理的Promise拒绝
        this.setupPromiseRejectionHandling();
        
        this._initialized = true;
    }

    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'global',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });

        // 重写console.error
        const originalConsoleError = console.error;
        console.error = (...args) => {
            try {
                const msg = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ');
                // 节流：相同消息在1秒内只处理一次 @LIFECYCLE
                const now = Date.now();
                if (this._lastLog.msg === msg && (now - this._lastLog.ts) < 1000) {
                    this._lastLog.count++;
                } else {
                    this._lastLog = { msg, ts: now, count: 0 };
                    this.handleError({ type: 'console', message: msg, timestamp: new Date().toISOString() });
                }
            } catch {}
            // 始终调用原始 console.error，避免吞日志 @LIFECYCLE
            try { originalConsoleError.apply(console, args); } catch {}
        };
    }

    /**
     * 设置Promise拒绝处理
     */
    setupPromiseRejectionHandling() {
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'promise',
                message: event.reason?.message || 'Unknown promise rejection',
                reason: event.reason,
                timestamp: new Date().toISOString()
            });
            
            // 防止默认错误提示
            event.preventDefault();
        });
    }

    /**
     * 处理错误
     */
    handleError(errorInfo) {
        // 重入保护：防止 handleError 内再次触发自己导致递归 @LIFECYCLE
        if (this._handling) return;
        this._handling = true;
        try {
            console.warn('错误捕获:', errorInfo);
            // 根据错误类型采取不同措施
            switch (errorInfo.type) {
                case 'global':
                    this.handleGlobalError(errorInfo);
                    break;
                case 'promise':
                    this.handlePromiseError(errorInfo);
                    break;
                case 'console':
                    this.handleConsoleError(errorInfo);
                    break;
                default:
                    this.handleUnknownError(errorInfo);
            }
            // 记录错误
            this.logError(errorInfo);
        } finally {
            this._handling = false;
        }
    }

    /**
     * 处理全局错误
     */
    handleGlobalError(errorInfo) {
        // 忽略一些常见的无害错误
        if (this.isBenignError(errorInfo)) {
            return;
        }

        // 显示用户友好的错误提示
        this.showUserError('系统发生错误', '请刷新页面重试');
    }

    /**
     * 处理Promise错误
     */
    handlePromiseError(errorInfo) {
        // API请求失败降级处理
        if (errorInfo.message.includes('fetch') || errorInfo.message.includes('API')) {
            this.showUserWarning('网络连接问题', '正在使用本地模式');
            return;
        }

        this.showUserError('操作失败', '请检查输入后重试');
    }

    /**
     * 处理控制台错误
     */
    handleConsoleError(errorInfo) {
        // 通常只是记录，不显示给用户
        console.warn('控制台错误:', errorInfo.message);
    }

    /**
     * 处理未知错误
     */
    handleUnknownError(errorInfo) {
        this.showUserError('未知错误', '请联系技术支持');
    }

    /**
     * 判断是否为无害错误
     */
    isBenignError(errorInfo) {
        const benignPatterns = [
            /Script error/, // 跨域脚本错误
            /ResizeObserver/, // 调整观察者错误
            /^$/, // 空错误信息
        ];

        return benignPatterns.some(pattern => 
            pattern.test(errorInfo.message)
        );
    }

    /**
     * 显示用户错误
     */
    showUserError(title, message) {
        this.showNotification(title, message, 'error');
    }

    /**
     * 显示用户警告
     */
    showUserWarning(title, message) {
        this.showNotification(title, message, 'warning');
    }

    /**
     * 显示用户信息
     */
    showUserInfo(title, message) {
        this.showNotification(title, message, 'info');
    }

    /**
     * 显示通知
     */
    showNotification(title, message, type = 'info') {
        // 移除现有的通知
        this.removeExistingNotifications();

        const notification = document.createElement('div');
        notification.className = `error-notification error-notification-${type}`;
        notification.innerHTML = `
            <div class="error-notification-content">
                <strong>${title}</strong>
                <p>${message}</p>
                <button onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // 添加样式（如果尚未添加）
        this.ensureNotificationStyles();

        document.body.appendChild(notification);

        // 自动消失
        if (type !== 'error') {
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
    }

    /**
     * 移除现有通知
     */
    removeExistingNotifications() {
        const existing = document.querySelectorAll('.error-notification');
        existing.forEach(el => el.remove());
    }

    /**
     * 确保通知样式
     */
    ensureNotificationStyles() {
        if (document.getElementById('error-notification-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'error-notification-styles';
        style.textContent = `
            .error-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
                max-width: 500px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                border-left: 4px solid #007bff;
                animation: slideIn 0.3s ease;
            }

            .error-notification-error {
                border-left-color: #dc3545;
            }

            .error-notification-warning {
                border-left-color: #ffc107;
            }

            .error-notification-info {
                border-left-color: #17a2b8;
            }

            .error-notification-content {
                padding: 16px;
                position: relative;
            }

            .error-notification-content strong {
                display: block;
                margin-bottom: 8px;
                color: #333;
            }

            .error-notification-content p {
                margin: 0;
                color: #666;
                line-height: 1.5;
            }

            .error-notification-content button {
                position: absolute;
                top: 12px;
                right: 12px;
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                color: #999;
            }

            .error-notification-content button:hover {
                color: #333;
            }

            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;

        document.head.appendChild(style);
    }

    /**
     * 记录错误
     */
    logError(errorInfo) {
        const errorLog = {
            ...errorInfo,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        // 保存到localStorage
        this.saveErrorToStorage(errorLog);

        // 控制台输出
        console.group('错误详情');
        console.error('错误时间:', errorLog.timestamp);
        console.error('错误类型:', errorLog.type);
        console.error('错误信息:', errorLog.message);
        if (errorLog.error) console.error('错误对象:', errorLog.error);
        console.groupEnd();
    }

    /**
     * 保存错误到存储
     */
    saveErrorToStorage(errorLog) {
        try {
            const existingLogs = JSON.parse(localStorage.getItem('error_logs') || '[]');
            
            // 只保留最近100条错误
            existingLogs.unshift(errorLog);
            if (existingLogs.length > 100) {
                existingLogs.length = 100;
            }

            localStorage.setItem('error_logs', JSON.stringify(existingLogs));
        } catch (error) {
            console.warn('保存错误日志失败:', error);
        }
    }

    /**
     * 获取错误日志
     */
    getErrorLogs() {
        try {
            return JSON.parse(localStorage.getItem('error_logs') || '[]');
        } catch {
            return [];
        }
    }

    /**
     * 清空错误日志
     */
    clearErrorLogs() {
        localStorage.removeItem('error_logs');
    }

    /**
     * 安全执行函数
     */
    safeExecute(fn, fallbackValue = null, context = null) {
        try {
            return fn.call(context);
        } catch (error) {
            this.handleError({
                type: 'safeExecute',
                message: `安全执行失败: ${error.message}`,
                error: error,
                timestamp: new Date().toISOString()
            });
            return fallbackValue;
        }
    }

    /**
     * 安全异步执行
     */
    async safeExecuteAsync(fn, fallbackValue = null, context = null) {
        try {
            return await fn.call(context);
        } catch (error) {
            this.handleError({
                type: 'safeExecuteAsync',
                message: `安全异步执行失败: ${error.message}`,
                error: error,
                timestamp: new Date().toISOString()
            });
            return fallbackValue;
        }
    }

    /**
     * 降级方案：本地存储回退
     */
    async withFallback(mainAction, fallbackAction, context = null) {
        try {
            return await mainAction.call(context);
        } catch (error) {
            this.handleError({
                type: 'fallback',
                message: `主操作失败，使用降级方案: ${error.message}`,
                error: error,
                timestamp: new Date().toISOString()
            });
            
            try {
                return await fallbackAction.call(context);
            } catch (fallbackError) {
                this.handleError({
                    type: 'fallbackFailed',
                    message: `降级方案也失败: ${fallbackError.message}`,
                    error: fallbackError,
                    timestamp: new Date().toISOString()
                });
                throw fallbackError;
            }
        }
    }
}

// 模块工厂函数
function createErrorHandlerModule() {
    return new ErrorHandler();
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('errorHandler', createErrorHandlerModule, []);
    console.log('📦 ErrorHandler已注册到模块容器');
}