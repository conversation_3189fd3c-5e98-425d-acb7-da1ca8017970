/**
 * @fileoverview 提示词组合器模块 - 提示词片段组合和生成引擎
 *
 * AI预防机制:
 * 1. 严格的依赖注入验证，防止AI生成无效的服务引用
 * 2. 提示词模板结构的完整性检查，确保组合结果的正确性
 * 3. 字段片段的渠道隔离验证，防止AI混淆不同渠道的片段
 * 4. 占位符替换的完整性检查，避免AI遗漏替换
 * 5. 缓存键的唯一性管理，防止AI创建的缓存冲突
 *
 * 重复开发保护:
 * 1. 提示词组合的缓存机制，避免重复生成相同组合
 * 2. 字段片段的唯一性验证，防止重复加载相同片段
 * 3. 组合结果的验证检查，避免重复生成无效结果
 * 4. 批量处理的去重机制，防止重复处理相同请求
 *
 * 竞争条件分析 (中文详细说明):
 * 1. 提示词组合过程的同步保护:
 *    - 防止并发组合请求导致的资源竞争
 *    - 确保组合结果的完整性和一致性
 *    - 避免多线程同时访问片段数据
 * 2. 缓存管理的并发访问保护:
 *    - 防止多线程同时读写缓存导致的数据损坏
 *    - 确保缓存更新的原子性和线程安全
 *    - 避免缓存清理时的状态竞争
 * 3. 字段片段获取的同步保护:
 *    - 防止并发获取同一字段片段导致的重复加载
 *    - 确保片段数据的完整性和时序性
 *    - 避免片段加载过程中的状态竞争
 * 4. 批量组合处理的队列管理:
 *    - 控制并发处理的线程数量和执行顺序
 *    - 防止批量操作过程中的状态竞争
 *    - 确保错误处理时的状态回滚一致性
 * 5. 验证过程的状态一致性保护:
 *    - 防止验证过程中提示词被其他操作修改
 *    - 确保验证结果与当前提示词的对应关系
 *    - 避免验证过程中的异步操作冲突
 *
 * 声明和依赖关系:
 * 核心依赖:
 * - configManager: 配置管理服务
 * - promptFragmentManager: 提示词片段管理服务
 * 提供的服务:
 * - 提示词动态组合和生成
 * - 字段模块化组合支持
 * - 缓存优化和性能提升
 * - 组合结果验证和预览
 *
 * 加载时序要求:
 * 1. 必须在依赖服务加载完成后初始化
 * 2. 异步初始化，确保依赖服务的可用性
 * 3. 依赖模块容器进行服务注册
 * 4. 建议在应用启动后延迟加载
 *
 * 修改历史:
 * - 2024-01-XX: 创建基础组合功能
 * - 2024-01-XX: 添加字段模块化组合
 * - 2024-01-XX: 实现缓存优化机制
 * - 2024-01-XX: 集成验证和预览功能
 * - 2024-01-XX: 添加批量处理支持
 *
 * <AUTHOR>
 * @version 1.0.0
 * @license MIT
 */

/**
 * 提示词组合器模块 - 提示词片段组合引擎
 *
 * === 文件依赖关系网络 ===
 * 依赖项：prompt-editor.js（片段源）, prompt-segmenter.js（片段处理）
 * 被依赖：app.js（动态提示词生成）, gemini-config.js（提示词构建）
 * 全局变量：创建 PromptComposer 实例（非全局）
 * 组合引擎：片段获取、智能合并、缓存优化、API集成
 *
 * === 核心功能 ===
 * - 动态提示词片段组合和生成
 * - 多源片段获取（本地/远程）
 * - 智能片段合并和冲突解决
 * - 缓存优化和性能提升
 * - API集成支持远程模板
 *
 * === 集成点 ===
 * - app.js：根据用户选择生成动态提示词
 * - prompt-editor.js：获取存储的提示词片段
 * - prompt-segmenter.js：处理和分析片段内容
 * - gemini-config.js：构建完整的API提示词
 *
 * === 使用场景 ===
 * - 根据渠道和字段需求动态生成提示词
 * - 多片段智能组合和优化
 * - 远程模板的获取和缓存
 * - 提示词生成性能优化
 *
 * === 注意事项 ===
 * 提示词片段组合引擎
 * 支持本地片段和远程API模板
 * 包含缓存机制提升性能
 * 智能合并算法避免内容冲突
 */

// 提示词片段组合引擎

class PromptComposer {
  constructor(configManager, promptFragmentManager) {
    if (!configManager || !promptFragmentManager) {
      throw new Error("PromptComposer requires ConfigManager and PromptFragmentManager.");
    }
    this.configManager = configManager;
    this.promptFragmentManager = promptFragmentManager;
    this.templateCache = new Map();
  }

  /**
   * 组合完整的提示词 - 支持字段模块化
   * @param {string} channelId - 渠道ID
   * @param {Array} requestedFields - 请求的字段列表
   * @param {string} inputText - 输入文本（用于字段模块化组合）
   */
  async composePrompt(channelId, requestedFields = null, inputText = '') {
    try {
      // 获取字段片段（渠道独立存储 + generic回退）
      const fieldSnippets = await this.getFieldSnippets(channelId, requestedFields);

      let composedPrompt = '';

      // 优先使用字段模块化组合（如果有输入文本）
      if (inputText && Object.keys(fieldSnippets).length > 0) {
        // 使用内置的字段模块化组合（不再依赖 PromptFragmentManager）
        composedPrompt = this.buildModularPromptInternal(inputText, channelId, fieldSnippets);
        console.log('🔧 使用字段模块化组合提示词');
      } else {
        // 传统组合方式（向后兼容）
        const baseTemplate = await this.getSnippet('base', null);
        composedPrompt = this.mergeSnippets(baseTemplate, fieldSnippets);
        console.log('🔄 使用传统组合方式');
      }

      return {
        success: true,
        composedPrompt: composedPrompt,
        segments: fieldSnippets,
        channelId: channelId,
        isModular: !!inputText && Object.keys(fieldSnippets).length > 0
      };

    } catch (error) {
      console.error('组合提示词失败:', error);
      return {
        success: false,
        error: error.message,
        composedPrompt: ''
      };
    }
  }

  

  /**
   * 获取字段片段 - 渠道独立存储 + generic回退机制
   *
   * 实现策略：
   * 1. 优先获取指定渠道的字段片段 (channelId)
   * 2. 如果渠道特定片段不存在，自动回退到通用渠道 (generic)
   * 3. 确保每个渠道的字段片段独立存储，避免跨渠道污染
   */
  async getFieldSnippets(channelId, requestedFields) {
    let fields = requestedFields;

    // If no fields are requested, get all available fields from promptFragmentManager
    if (!fields || fields.length === 0) {
      fields = this.promptFragmentManager.getAllFieldNames(); // Assuming this method exists
    }

    const snippets = {};
    for (const field of fields) {
      // Use promptFragmentManager to get the fragment
      const snippet = await this.promptFragmentManager.getPromptFragment(field, channelId);
      if (snippet) {
        snippets[field] = snippet;
      }
    }
    return snippets;
  }

  

  /**
   * 内置字段模块化组合 - 替代 PromptFragmentManager.buildModularPrompt
   * @param {string} inputText - 输入文本
   * @param {string} channelId - 渠道ID
   * @param {Object} fieldSnippets - 字段片段映射
   * @returns {string} 组合后的提示词
   */
  buildModularPromptInternal(inputText, channelId, fieldSnippets) {
    const parts = [];

    // 1. 基础角色和格式
    parts.push('You are an expert booking data extraction system. Extract ALL information with maximum accuracy.');
    parts.push('Output EXACTLY one JSON object. No explanations, no additional text.');
    parts.push('');

    // 2. 字段模块化组合 - 使用字段片段
    parts.push('REQUIRED FIELDS (set null if not found):');
    for (const [field, snippet] of Object.entries(fieldSnippets)) {
      parts.push(`- ${field}: ${snippet}`);
    }
    parts.push('');

    // 3. 输入文本
    parts.push('INPUT TEXT TO ANALYZE:');
    parts.push('---');
    parts.push(inputText);
    parts.push('---');

    return parts.join('\n');
  }

  /**
   * 合并片段到模板
   */
  mergeSnippets(baseTemplate, fieldSnippets) {
    let result = baseTemplate || '';
    
    // 简单的占位符替换
    for (const [field, snippet] of Object.entries(fieldSnippets)) {
      const placeholder = `{${field}}`;
      if (result.includes(placeholder)) {
        result = result.replace(placeholder, snippet);
      } else {
        // 如果没有占位符，追加到末尾
        result += '\n\n' + snippet;
      }
    }
    
    // 清理多余的空白行
    result = result.replace(/\n{3,}/g, '\n\n').trim();
    
    return result;
  }



  /**
   * 检测所需字段
   */
  async detectRequiredFields(channelId) {
    // 根据渠道类型推断需要的字段
    const channelType = await this.getChannelType(channelId);
    
    const fieldSets = {
      ota: ['ota', 'ota_price', 'customer', 'flight', 'location'],
      ride: ['customer', 'pickup', 'destination', 'requirements'],
      delivery: ['customer', 'pickup', 'destination', 'requirements'],
      default: ['validation', 'schema', 'customer', 'requirements']
    };
    
    return fieldSets[channelType] || fieldSets.default;
  }

  /**
   * 获取渠道类型
   */
  async getChannelType(channelId) {
    if (!channelId) return 'default';
    
    // Use configManager to get channel data
    const allChannels = this.configManager.getAllChannels();
    const channelInfo = allChannels.find(c => c.toLowerCase() === channelId.toLowerCase());

    if (channelInfo) {
      // Infer type from channel name
      if (channelInfo.includes('flight') || channelInfo.includes('ota')) {
        return 'ota';
      } else if (channelInfo.includes('ride') || channelInfo.includes('taxi')) {
        return 'ride';
      } else if (channelInfo.includes('delivery') || channelInfo.includes('快递')) {
        return 'delivery';
      }
    }
    
    return 'default';
  }

  /**
   * 验证组合结果
   */
  validateComposedPrompt(prompt) {
    const issues = [];
    
    // 检查长度
    if (prompt.length > 4000) {
      issues.push('提示词过长，可能会影响模型性能');
    }
    
    // 检查未替换的占位符
    const unmatchedPlaceholders = prompt.match(/\{[^}]+\}/g);
    if (unmatchedPlaceholders) {
      issues.push(`存在未替换的占位符: ${unmatchedPlaceholders.join(', ')}`);
    }
    
    // 检查JSON格式（如果包含）
    if (prompt.includes('{') && prompt.includes('}')) {
      const jsonMatch = prompt.match(/\{[\s\S]*?\}/);
      if (jsonMatch) {
        try {
          JSON.parse(jsonMatch[0]);
        } catch (e) {
          issues.push('包含无效的JSON格式');
        }
      }
    }
    
    return {
      isValid: issues.length === 0,
      issues: issues,
      length: prompt.length,
      lineCount: prompt.split('\n').length
    };
  }

  /**
   * 智能片段选择
   */
  async smartSegmentSelection(basePrompt, availableSnippets) {
    const selectedSnippets = {};
    
    // 分析基础模板中的占位符
    const placeholders = this.extractPlaceholders(basePrompt);
    
    // 优先选择有占位符的字段
    for (const field of placeholders) {
      if (availableSnippets[field]) {
        selectedSnippets[field] = availableSnippets[field];
      }
    }
    
    // 添加其他重要字段
    const importantFields = ['validation', 'schema', 'requirements'];
    for (const field of importantFields) {
      if (availableSnippets[field] && !selectedSnippets[field]) {
        selectedSnippets[field] = availableSnippets[field];
      }
    }
    
    return selectedSnippets;
  }

  /**
   * 提取占位符
   */
  extractPlaceholders(text) {
    const matches = text.match(/\{([^}]+)\}/g) || [];
    return matches.map(m => m.replace(/[{}]/g, ''));
  }

  /**
   * 缓存管理
   */
  clearCache() {
    this.templateCache.clear();
  }

  cacheSnippet(field, channelId, content) {
    const cacheKey = `${field}_${channelId || 'generic'}`;
    this.templateCache.set(cacheKey, content);
  }

  /**
   * 批量组合提示词
   */
  async batchCompose(requests) {
    const results = [];
    
    for (const request of requests) {
      try {
        const result = await this.composePrompt(request.channelId, request.fields);
        results.push({
          request: request,
          result: result,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        results.push({
          request: request,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }
    
    return results;
  }

  /**
   * 创建组合预览
   */
  async createPreview(channelId, fields = null) {
    const result = await this.composePrompt(channelId, fields);
    
    if (result.success) {
      const validation = this.validateComposedPrompt(result.composedPrompt);
      
      return {
        preview: result.composedPrompt,
        validation: validation,
        segments: result.segments,
        segmentCount: Object.keys(result.segments).length,
        totalLength: result.composedPrompt.length
      };
    }
    
    return {
      error: result.error,
      preview: ''
    };
  }

  /**
   * 导出组合配置
   */
  exportCompositionConfig(channelId, fields) {
    return {
      channelId: channelId,
      fields: fields,
      timestamp: new Date().toISOString(),
      version: '1.0',
      config: {
        baseTemplate: true,
        fieldSelection: 'auto',
        validation: true,
        cacheEnabled: true
      }
    };
  }
}

// 模块工厂函数
function createPromptComposerModule(container) {
  const configManager = container.get('config');
  const promptFragmentManager = container.get('promptFragmentManager');
  return new PromptComposer(configManager, promptFragmentManager);
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
  window.registerModule('promptComposer', createPromptComposerModule, ['config', 'promptFragmentManager']);
  console.log('📦 PromptComposer 已注册到模块容器');
}