/**
 * 【模块注册模式统一完成 - 2025-01-01】
 * 
 * 历史问题（已解决）：
 * ❌ 模块初始化时报错："未找到模块: configManager"
 * ❌ 架构冲突：混合使用自注册模式和工厂函数模式
 * ❌ 时机冲突：自注册与集中注册时机不同步
 * ❌ 命名不匹配：configManager vs config
 * ❌ 依赖管理：模块不在main.js依赖图管理范围内
 * 
 * 当前状态（阶段3完成）：
 * ✅ 统一为工厂函数模式：window.createPromptProcessorModule
 * ✅ 集中注册管理：main.js第151-152行统一注册
 * ✅ 命名统一：统一使用'config'模块名
 * ✅ 依赖关系明确：['fieldMapper', 'promptComposer', 'config', 'channelDetector']
 * ✅ 全局暴露：window.promptProcessor兼容性支持
 * 
 * 架构优势：
 * - 单一注册模式，消除时机冲突
 * - 完整依赖管理，容器统一控制
 * - 向后兼容，现有代码无需修改
 * - 易于测试和维护
 * 
 * ============================================================================
 * 
 * 提示词处理器 - 核心处理引擎（Main-Modal深度关联版）
 * 
 * 职责：
 * 1. 订单内容的字段解析和提取
 * 2. 提示词片段的智能匹配和组装  
 * 3. 字段映射到提示词模板
 * 4. 与Modal组件的深度协同和数据同步
 * 5. 实时预览和片段映射功能
 * 
 * 依赖：fieldMapper, promptComposer, configManager, channelDetector
 */
class PromptProcessor {
    constructor(fieldMapper, promptComposer, configManager, channelDetector) {
        if (!fieldMapper || !promptComposer || !configManager || !channelDetector) {
            throw new Error("PromptProcessor requires all dependencies to be injected.");
        }
        
        this.fieldMapper = fieldMapper;
        this.promptComposer = promptComposer;
        this.configManager = configManager;
        this.channelDetector = channelDetector;
        
        // 处理状态
        this.lastProcessedContent = null;
        this.lastProcessedFields = null;
        this.lastDetectedChannel = null;
        this.lastPromptResult = null;
        this.processingCache = new Map();
        
        // Modal关联状态
        this.linkedModal = null;
        this.isRealTimeMode = false;
        this.fieldMappingResults = new Map();
        
        // 事件监听器
        this.eventListeners = new Map();
        
        console.log('✅ PromptProcessor initialized (Main-Modal协同版)');
    }

    /**
     * 主要处理方法：处理订单内容（Modal协同版）
     * @param {string} orderContent - 订单内容
     * @returns {Object} 处理结果
     */
    async processOrderContent(orderContent) {
        try {
            console.log('🔄 开始处理订单内容（Modal协同版）...');
            
            // 1. 检查缓存
            const cacheKey = this.generateCacheKey(orderContent);
            if (this.processingCache.has(cacheKey)) {
                console.log('📋 使用缓存结果');
                const cachedResult = this.processingCache.get(cacheKey);
                
                // 即使是缓存结果，也要同步到Modal
                if (this.linkedModal) {
                    this.syncToModal('processing_completed', cachedResult);
                }
                
                return cachedResult;
            }
            
            // 2. 渠道检测
            console.log('🔍 步骤1: 渠道检测');
            const channelResult = this.channelDetector.detectChannel(orderContent);
            this.lastDetectedChannel = channelResult;
            
            // 同步渠道检测结果到Modal
            if (this.linkedModal) {
                this.syncToModal('channel_detected', {
                    channel: channelResult,
                    step: 1,
                    totalSteps: 4
                });
            }
            
            // 3. 提示词组装
            console.log('🔧 步骤2: 提示词组装');
            const promptResult = await this.promptComposer.composePrompt(
                channelResult?.channel || 'generic',
                null,
                orderContent
            );
            this.lastPromptResult = promptResult;
            
            // 同步提示词结果到Modal
            if (this.linkedModal) {
                this.syncToModal('prompt_composed', {
                    promptResult,
                    step: 2,
                    totalSteps: 4
                });
            }
            
            // 4. 字段提取
            console.log('🤖 步骤3: 智能字段提取');
            const extractedFields = await this.fieldMapper.processCompleteData(
                orderContent, 
                promptResult.composedPrompt
            );
            this.lastProcessedFields = extractedFields.data;
            
            // 建立字段到片段的映射关系
            const fieldMapping = this.getFieldToFragmentMapping();
            this.fieldMappingResults.clear();
            for (const [fieldName, mappingInfo] of Object.entries(fieldMapping)) {
                this.fieldMappingResults.set(fieldName, mappingInfo);
            }
            
            // 同步字段提取结果到Modal
            if (this.linkedModal) {
                this.syncToModal('fields_extracted', {
                    fields: extractedFields,
                    fieldMapping,
                    step: 3,
                    totalSteps: 4
                });
            }
            
            // 5. 构建完整结果
            console.log('✅ 步骤4: 结果整合');
            const result = {
                success: true,
                timestamp: new Date().toISOString(),
                channel: channelResult,
                fields: extractedFields,
                prompt: promptResult,
                fieldMapping: fieldMapping,
                originalContent: orderContent
            };
            
            // 6. 缓存结果
            this.processingCache.set(cacheKey, result);
            this.lastProcessedContent = orderContent;
            
            // 最终同步完整结果到Modal
            if (this.linkedModal) {
                this.syncToModal('processing_completed', {
                    result,
                    step: 4,
                    totalSteps: 4
                });
            }
            
            // 触发处理完成事件
            this.triggerUIUpdate('processing_completed', result);
            
            console.log('✅ 订单内容处理完成（含Modal同步）');
            return result;
            
        } catch (error) {
            console.error('❌ 处理订单内容时出错:', error);
            
            const errorResult = {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString(),
                originalContent: orderContent
            };
            
            // 同步错误信息到Modal
            if (this.linkedModal) {
                this.syncToModal('processing_error', {
                    error: errorResult
                });
            }
            
            return errorResult;
        }
    }

    /**
     * 获取字段映射结果
     * @returns {Object} 字段映射结果
     */
    getFieldMappings() {
        return this.lastProcessedFields || {};
    }

    /**
     * 获取渠道检测结果
     * @returns {Object} 渠道检测结果
     */
    getChannelDetection() {
        return this.lastDetectedChannel || null;
    }

    /**
     * 更新字段值（用于UI交互）
     * @param {string} fieldName - 字段名
     * @param {string} value - 字段值
     */
    updateFieldValue(fieldName, value) {
        if (!this.lastProcessedFields) {
            this.lastProcessedFields = {};
        }
        
        this.lastProcessedFields[fieldName] = value;
        
        // 触发重新组装提示词
        this.recomposePrompt();
    }

    /**
     * 重新组装提示词（字段更新后）
     */
    async recomposePrompt() {
        if (!this.lastProcessedFields || !this.lastDetectedChannel) {
            return;
        }
        
        try {
            const promptResult = await this.promptComposer.composePrompt(
                this.lastProcessedFields,
                this.lastDetectedChannel.channel || 'default'
            );
            
            // 触发UI更新事件
            this.triggerUIUpdate('prompt_recomposed', {
                prompt: promptResult,
                fields: this.lastProcessedFields
            });
            
        } catch (error) {
            console.error('❌ 重新组装提示词时出错:', error);
        }
    }

    /**
     * 获取可用的提示词片段
     * @param {string} channel - 渠道名称
     * @returns {Array} 提示词片段列表
     */
    getAvailablePromptFragments(channel = null) {
        const targetChannel = channel || this.lastDetectedChannel?.channel || 'default';
        return this.promptComposer.getFragmentsForChannel(targetChannel);
    }

    /**
     * 预览提示词（基于当前字段值）
     * @param {Object} customFields - 自定义字段值
     * @returns {Object} 预览结果
     */
    async previewPrompt(customFields = null) {
        const fields = customFields || this.lastProcessedFields || {};
        const channel = this.lastDetectedChannel?.channel || 'default';
        
        return await this.promptComposer.composePrompt(fields, channel);
    }

    /**
     * 清理处理状态
     */
    clearProcessingState() {
        this.lastProcessedContent = null;
        this.lastProcessedFields = null;
        this.lastDetectedChannel = null;
        this.processingCache.clear();
        
        console.log('🧹 处理状态已清理');
    }

    /**
     * 生成缓存键
     * @param {string} content - 内容
     * @returns {string} 缓存键
     */
    generateCacheKey(content) {
        // 简单的哈希算法
        let hash = 0;
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转为32位整数
        }
        return `content_${Math.abs(hash)}`;
    }

    /**
     * 关联Modal组件（建立深度协同）
     * @param {Object} modal - Modal组件实例
     */
    linkToModal(modal) {
        this.linkedModal = modal;
        console.log('🔗 PromptProcessor已关联到Modal组件');
        
        // 如果有历史处理结果，立即同步给Modal
        if (this.lastProcessedFields && this.lastDetectedChannel) {
            this.syncToModal('initial_sync', {
                fields: this.lastProcessedFields,
                channel: this.lastDetectedChannel,
                content: this.lastProcessedContent
            });
        }
    }

    /**
     * 启用/禁用实时模式
     * @param {boolean} enabled - 是否启用实时模式
     */
    setRealTimeMode(enabled) {
        this.isRealTimeMode = enabled;
        console.log(`🔄 实时模式已${enabled ? '启用' : '禁用'}`);
        
        if (enabled && this.linkedModal) {
            this.triggerUIUpdate('realtime_mode_enabled', {
                processor: this,
                modal: this.linkedModal
            });
        }
    }

    /**
     * 同步数据到关联的Modal
     * @param {string} eventType - 同步事件类型
     * @param {Object} data - 同步数据
     */
    syncToModal(eventType, data) {
        if (!this.linkedModal) {
            console.warn('⚠️ 未关联Modal组件，无法同步数据');
            return;
        }

        try {
            // 调用Modal的数据接收方法
            if (typeof this.linkedModal.receiveFromProcessor === 'function') {
                this.linkedModal.receiveFromProcessor(eventType, data);
            }
            
            // 触发全局事件
            this.triggerUIUpdate('modal_sync', {
                eventType,
                data,
                target: 'modal'
            });
            
        } catch (error) {
            console.error('❌ 同步数据到Modal失败:', error);
        }
    }

    /**
     * 接收来自Modal的数据更新
     * @param {string} eventType - 事件类型
     * @param {Object} data - 更新数据
     */
    receiveFromModal(eventType, data) {
        console.log('📥 PromptProcessor收到Modal数据:', eventType, data);
        
        switch (eventType) {
            case 'prompt_fragments_updated':
                this.handleFragmentsUpdate(data);
                break;
            case 'channel_switched':
                this.handleChannelSwitch(data);
                break;
            case 'field_mapping_request':
                this.handleFieldMappingRequest(data);
                break;
            case 'preview_request':
                this.handlePreviewRequest(data);
                break;
            default:
                console.warn('⚠️ 未知的Modal事件类型:', eventType);
        }
    }

    /**
     * 处理提示词片段更新（来自Modal）
     * @param {Object} data - 片段更新数据
     */
    async handleFragmentsUpdate(data) {
        try {
            console.log('🔄 处理提示词片段更新...');
            
            // 如果有当前处理的内容，重新组合提示词
            if (this.lastProcessedContent && this.lastDetectedChannel) {
                const promptResult = await this.promptComposer.composePrompt(
                    this.lastDetectedChannel.channel || 'generic',
                    data.fields || null,
                    this.lastProcessedContent
                );
                
                this.lastPromptResult = promptResult;
                
                // 同步更新结果到Modal
                this.syncToModal('prompt_recomposed', {
                    promptResult,
                    trigger: 'fragments_updated'
                });
                
                // 触发主界面更新
                this.triggerUIUpdate('fragments_updated', {
                    promptResult,
                    updatedFragments: data.fragments
                });
            }
            
        } catch (error) {
            console.error('❌ 处理片段更新失败:', error);
        }
    }

    /**
     * 处理渠道切换（来自Modal）
     * @param {Object} data - 渠道切换数据
     */
    async handleChannelSwitch(data) {
        try {
            console.log('🔀 处理渠道切换:', data.channel);
            
            // 更新当前检测的渠道
            this.lastDetectedChannel = {
                channel: data.channel,
                confidence: data.confidence || 1.0,
                source: 'modal_switch'
            };
            
            // 重新组合提示词
            if (this.lastProcessedContent) {
                await this.recomposePrompt();
            }
            
        } catch (error) {
            console.error('❌ 处理渠道切换失败:', error);
        }
    }

    /**
     * 处理字段映射请求（来自Modal）
     * @param {Object} data - 映射请求数据
     */
    handleFieldMappingRequest(data) {
        console.log('🗺️ 处理字段映射请求...');
        
        // 返回当前的字段映射结果
        const mappingData = {
            fields: this.lastProcessedFields || {},
            channel: this.lastDetectedChannel,
            content: this.lastProcessedContent,
            mappingResults: Object.fromEntries(this.fieldMappingResults)
        };
        
        this.syncToModal('field_mapping_response', mappingData);
    }

    /**
     * 处理预览请求（来自Modal）
     * @param {Object} data - 预览请求数据
     */
    async handlePreviewRequest(data) {
        try {
            console.log('👁️ 处理预览请求...');
            
            const previewResult = await this.previewPrompt(data.customFields);
            
            this.syncToModal('preview_response', {
                previewResult,
                requestId: data.requestId
            });
            
        } catch (error) {
            console.error('❌ 处理预览请求失败:', error);
        }
    }

    /**
     * 获取字段到片段的映射关系
     * @returns {Object} 映射关系对象
     */
    getFieldToFragmentMapping() {
        if (!this.lastProcessedFields) {
            return {};
        }
        
        const mapping = {};
        for (const [fieldName, fieldValue] of Object.entries(this.lastProcessedFields)) {
            // 查找与字段相关的提示词片段
            const relatedFragments = this.findRelatedFragments(fieldName);
            mapping[fieldName] = {
                value: fieldValue,
                fragments: relatedFragments,
                hasMapping: relatedFragments.length > 0
            };
        }
        
        return mapping;
    }

    /**
     * 查找与字段相关的提示词片段
     * @param {string} fieldName - 字段名
     * @returns {Array} 相关片段列表
     */
    findRelatedFragments(fieldName) {
        // 这里需要与promptComposer协作查找相关片段
        try {
            if (this.promptComposer && typeof this.promptComposer.getFragmentsForField === 'function') {
                return this.promptComposer.getFragmentsForField(fieldName);
            }
            
            // 回退逻辑：基于字段名查找
            return this.getAvailablePromptFragments().filter(fragment => 
                fragment.field === fieldName || 
                fragment.name.toLowerCase().includes(fieldName.toLowerCase())
            );
            
        } catch (error) {
            console.error('❌ 查找相关片段失败:', error);
            return [];
        }
    }

    /**
     * 触发UI更新事件
     * @param {string} eventType - 事件类型
     * @param {Object} data - 事件数据
     */
    triggerUIUpdate(eventType, data) {
        const event = new CustomEvent('prompt-processor-update', {
            detail: {
                type: eventType,
                data: data,
                timestamp: new Date().toISOString(),
                processor: this
            }
        });
        
        window.dispatchEvent(event);
        
        // 如果关联了Modal，也通知Modal
        if (this.linkedModal && this.isRealTimeMode) {
            this.syncToModal('ui_update', {
                eventType,
                data
            });
        }
    }

    /**
     * 获取处理统计信息
     * @returns {Object} 统计信息
     */
    getProcessingStats() {
        return {
            cacheSize: this.processingCache.size,
            lastProcessedAt: this.lastProcessedContent ? new Date().toISOString() : null,
            hasProcessedContent: !!this.lastProcessedContent,
            detectedChannel: this.lastDetectedChannel?.channel || null
        };
    }
}

// ============================================================================
// 自注册代码已移除 - 阶段3统一架构完成
// ============================================================================
// 
// 历史: 原有自注册代码已在阶段3中移除
// 原因: 统一为main.js集中注册模式，避免注册时机冲突
// 替代: 使用window.createPromptProcessorModule工厂函数
// 注册位置: js/core/main.js 第151-152行

// ============================================================================
// 标准工厂函数 - 阶段1统一工厂模式
// ============================================================================

/**
 * 标准工厂函数：创建PromptProcessor实例
 * 用于main.js中的集中注册，与自注册形成双轨制兼容
 * @param {ModuleContainer} container - 模块容器实例
 * @returns {PromptProcessor} PromptProcessor实例
 */
window.createPromptProcessorModule = function(container) {
    const fieldMapper = container.get('fieldMapper');
    const promptComposer = container.get('promptComposer');
    const config = container.get('config');  // 统一命名，解决configManager冲突
    const channelDetector = container.get('channelDetector');
    
    return new PromptProcessor(fieldMapper, promptComposer, config, channelDetector);
};