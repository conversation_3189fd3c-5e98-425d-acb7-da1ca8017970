/**
 * ============================================================================
 * 统一配置管理模块 (config.js) - 防AI幻觉核心声明
 * ============================================================================
 *
 * 【防AI幻觉声明】
 * 本文件是整个应用的配置数据中心，负责所有配置项的统一管理和访问。
 * 严禁AI基于类名推测功能，必须完整阅读所有方法才能理解配置数据结构和访问模式。
 * 本文件不包含业务逻辑，仅提供配置数据的读取和工具函数。
 *
 * 【重复开发防护】
 * 警告：本项目已存在完整的配置管理系统，严禁创建新的配置管理模块。
 * 任何新的配置项必须在本文件中定义，而不是创建分散的配置对象。
 * 现有配置模式：[类属性存储 → getter方法访问 → 本地存储持久化] - 严禁修改此约定。
 *
 * 【竞态问题防护】
 * 配置数据访问竞态防护：
 * 1. 单例模式：ConfigManager实例唯一，防止配置数据不一致
 * 2. 防御性编程：所有输入参数使用String()转换，防止类型错误
 * 3. 本地存储访问保护：try/catch包装localStorage访问，防止异常中断
 * 4. 异步数据源处理：getOtaData等方法处理异步数据源的加载状态
 * 防护措施：使用类型检查、异常处理、默认值回退确保配置访问的稳定性。
 *
 * 【声明与接口】
 * 导出：ConfigManager类、createConfigModule工厂函数
 * 导入：依赖window.OTA_SOURCE、window.AIRPORT_DATA_SOURCE全局对象
 * 主要接口：
 *   - constructor(otaSource, airportDataSource)：依赖注入初始化
 *   - getAllUsers()：获取用户列表
 *   - getAllChannels()：获取渠道列表
 *   - getChannelDetectionRules()：获取渠道检测规则（支持本地存储）
 *   - getVehicleTypes()：获取车辆类型配置
 *   - DataUtils.patternToRegex()：正则表达式构建工具
 *
 * 【依赖关系网络】
 * 直接依赖：
 *   - window.OTA_SOURCE：OTA数据源 @REQUIRED
 *   - window.AIRPORT_DATA_SOURCE：机场数据源 @REQUIRED
 *   - window.localStorageManager：本地存储管理器 @OPTIONAL
 *
 * 被依赖关系：
 *   - 被main.js作为核心配置模块注册
 *   - 被channelDetector依赖获取检测规则
 *   - 被所有需要配置数据的模块间接依赖
 *   - 影响整个应用的配置一致性
 *
 * 【加载时机与生命周期】
 * 加载时机：模块容器初始化阶段同步加载
 * 初始化时机：模块容器调用createConfigModule工厂函数时
 * 生命周期：
 *   1. 文件加载 → 2. 工厂函数注册 → 3. 依赖注入 → 4. 配置数据初始化 → 5. 运行时配置访问 → 6. 本地存储同步 → 7. 实例销毁
 * 清理处理：无特殊清理（配置数据为静态）
 *
 * 【技术栈与环境要求】
 * 运行环境：浏览器环境（依赖window对象和localStorage）
 * 技术栈：原生JavaScript ES6+类语法，无框架依赖
 * 浏览器支持：现代浏览器（支持class语法、箭头函数、解构赋值）
 * 存储要求：支持localStorage用于配置持久化
 *
 * 【核心功能说明】
 * 1. 配置数据管理：统一存储和管理所有应用配置
 * 2. 数据访问接口：提供类型安全的数据访问方法
 * 3. 本地存储集成：支持配置数据的持久化存储
 * 4. 工具函数提供：正则表达式构建等通用工具
 * 5. 依赖注入支持：通过工厂函数支持模块容器集成
 * 6. 防御性编程：完善的错误处理和默认值机制
 *
 * 【相关文件索引】
 * - main.js：负责本模块的注册和初始化
 * - channel-detector.js：使用渠道检测规则配置
 * - local-storage-manager.js：提供本地存储服务
 * - index.html：提供全局数据源对象
 *
 * 【修改记录】
 * v1.0.0 (2025-08-31) - 重构版本
 *   - 重构为ES6类语法
 *   - 添加本地存储持久化支持
 *   - 完善防御性编程
 *   - 添加详细的防AI幻觉注释系统
 *   - 优化配置数据结构
 *
 * 【使用说明】
 * 配置访问：通过模块容器获取实例后调用getter方法
 * 本地存储：渠道检测规则会自动持久化到本地存储
 * 工具使用：DataUtils.patternToRegex用于构建正则表达式
 * 调试信息：控制台输出初始化状态和配置数据
 *
 * ============================================================================
 */

/**
 * 统一配置管理模块 (DEBUGGING - MINIMAL VERSION)
 */
class ConfigManager {
    constructor(otaSource, airportDataSource) {
        this.otaSource = otaSource; // 全局依赖：OTA数据源 @DEPENDENCY
        this.airportDataSource = airportDataSource; // 全局依赖：机场数据源 @DEPENDENCY
        this.config = this.loadDefaultConfig(); // 初始化最小配置 @INIT
        // 暴露工具集合：统一正则构建工具，供外部模块使用（纯文本→不区分大小写的正则） @UTIL
        this.DataUtils = {
            patternToRegex: (text) => this._patternToRegex(String(text || '')) // 防御性：空值转空串 @LIFECYCLE
        };
        console.log('✅ MINIMAL ConfigManager Initialized for Debugging'); // 初始化日志 @LIFECYCLE
    }

    loadDefaultConfig() {
        // RETURNING MINIMAL CONFIG FOR DEBUGGING @CONFIG_FILE
        return {
            COMPLETE_USER_LIST: [{ id: 1, name: 'Super Admin', role_id: 1 }], // 用户清单 @DATA_STRUCTURE
            COMPLETE_CHANNEL_LIST: ['Kkday', 'Ctrip API'], // 渠道清单 @DATA_STRUCTURE
            CHANNEL_DETECTION_RULES: {
                 klook: { name: 'Klook', displayName: 'Klook', patterns: ['klook', '客路'], confidence: 0.85 } // 最小规则示例 @DATA_STRUCTURE
            },
            VEHICLE_TYPES: [{ id: 5, name: '5 Seater', displayName: '5座轿车' }], // 车型配置 @DATA_STRUCTURE
            USER_PERMISSION_CONFIG: {
                defaultPermissions: { features: { canUsePaging: true } } // 权限配置 @DATA_STRUCTURE
            },
            REFERENCE_PATTERNS: {}, // 真实数据源接入位：参考号规则 @DECLARATION
            KEYWORD_DETECTION: {} // 真实数据源接入位：关键词规则 @DECLARATION
        };
    }

    // --- Getters for minimal data ---
    getAllUsers() { return this.config.COMPLETE_USER_LIST; }
    getAllChannels() { return this.config.COMPLETE_CHANNEL_LIST; }
    getUserPermissions() { return this.config.USER_PERMISSION_CONFIG.defaultPermissions; }
    // 优先返回“渠道规则编辑器”持久化内容；回退到默认配置 @LIFECYCLE
    getChannelDetectionRules() {
        try {
            const saved = (window.localStorageManager && window.localStorageManager.data)
                ? window.localStorageManager.data.channelDetectionRules
                : null; // 从本地实例内存中读取，避免解密/异步问题 @DEPENDENCY
            return saved && typeof saved === 'object' ? saved : this.config.CHANNEL_DETECTION_RULES;
        } catch { return this.config.CHANNEL_DETECTION_RULES; }
    }
    getVehicleTypes() { return this.config.VEHICLE_TYPES; }
    getOtaData() { return this.otaSource; }
    getHotelData() { return this.otaSource?.hotels || []; } // 酒店数据源，返回数组 @REFERENCE
    getAirportData() { return this.airportDataSource; } // 航空数据源 @REFERENCE

    // 统一正则构建：将纯文本安全转义后生成不区分大小写的正则 @UTIL
    _patternToRegex(patternText) {
        // 注意：字符类中需转义 ']' 和 '\\'；保持与外部模块一致性，避免语法错误 @DECLARATION
        return new RegExp(String(patternText || '').replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
    }

    // 真实数据源：参考号模式映射（可接入后端或静态资源） @SERVICE
    getReferencePatterns() {
        return this.config.REFERENCE_PATTERNS || {}; // 最小实现：空对象 @LIFECYCLE
    }

    // 真实数据源：关键词检测映射 @SERVICE
    getKeywordDetection() {
        return this.config.KEYWORD_DETECTION || {}; // 最小实现：空对象 @LIFECYCLE
    }
}

// 工厂函数：供DI容器使用 @FACTORY
window.createConfigModule = function() {
    return new ConfigManager(window.OTA_SOURCE, window.AIRPORT_DATA_SOURCE);
};