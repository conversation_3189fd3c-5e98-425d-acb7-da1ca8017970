# 个人偏好和开发习惯

## 用户个人信息

### 基本偏好
- **语言偏好**: 中文交流
- **技术风格**: 强调纯前端无第三方依赖的实现方案
- **开发理念**: 系统性思考优于快速实现，稳定性和可维护性优先

### 编码风格偏好
- **代码风格**: 遵循现有项目的JavaScript编码规范
- **注释要求**: 每个文件头部需要详细的依赖和技术指南说明
- **命名约定**: 保持与现有代码的一致性

## 开发约束和要求

### 核心开发原则
1. **严格按需求执行**: 不多不少，避免过度设计
2. **优先编辑现有文件**: 避免创建新文件
3. **保持纯前端特性**: 100%原生JavaScript，零第三方依赖
4. **系统性思考**: 先规划后执行，重视长期可维护性

### 技术约束
- **禁用第三方库**: 任何外部JavaScript库都不允许引入
- **纯静态实现**: 不依赖Node.js、构建工具或后端服务
- **文件加载顺序**: 严格遵循HTML中定义的脚本加载顺序
- **依赖注入**: 所有模块必须通过容器获取服务，避免全局变量

### 代码审查标准
- **安全性**: 优先处理硬编码密钥等安全风险
- **性能**: 重视缓存策略和内存管理
- **可读性**: 代码必须有清晰的注释和文档
- **测试**: 核心功能必须有验证机制

## 工作流程偏好

### RIPER-5 模式应用
- **严格模式转换**: 每个回复必须声明当前模式
- **规划优先**: 复杂任务必须先进入规划模式
- **详尽计划**: 实施前必须有完整的技术规范和检查清单
- **用户确认**: 重要决策需要明确的用户批准

### 沟通偏好
- **中文回复**: 所有技术讨论使用中文
- **简洁准确**: 避免冗长的说明，直接给出解决方案
- **问题导向**: 不确定时主动提问，不进行猜测
- **承认不知**: 承认不知道比给出错误答案更好

## 项目特定偏好

### 渠道检测编辑器项目
- **架构重视**: 特别关注依赖注入容器的正确使用
- **安全优先**: 当前阶段重点关注安全加固工作
- **性能监控**: 重视缓存系统的效果和监控
- **纯前端坚持**: 绝对不允许引入任何构建依赖或第三方库

### 代码修改偏好
- **渐进式改进**: 小步快跑，避免大规模重构
- **向后兼容**: 新功能不能破坏现有用户数据
- **测试验证**: 修改后必须验证功能完整性
- **文档同步**: 代码变更必须同步更新相关文档

## 技术学习偏好

### 学习方式
- **实践导向**: 通过实际项目问题学习新技术
- **深度理解**: 不满足于表面使用，要理解技术本质
- **系统思考**: 关注技术决策对整体架构的影响
- **持续改进**: 定期反思和优化现有方案

### 技术关注点
- **原生技术**: 深入理解Web标准和原生API
- **架构模式**: 关注设计模式在前端项目中的应用
- **性能优化**: 重视用户体验和系统性能
- **安全实践**: 关注前端安全最佳实践

## 沟通和协作

### 反馈偏好
- **直接反馈**: 问题和建议请直接指出
- **建设性批评**: 提供具体的改进建议
- **及时沟通**: 重要决策需要及时讨论
- **文档化**: 重要决定和变更需要记录

### 工作节奏
- **专注时段**: 倾向于集中处理复杂技术问题
- **定期回顾**: 喜欢定期总结和规划
- **质量优先**: 宁可慢一点也要保证质量
- **持续改进**: 重视代码重构和架构优化

## 特殊需求和限制

### 环境限制
- **网络环境**: 可能存在网络访问限制
- **工具限制**: 主要使用浏览器开发者工具进行调试
- **时间限制**: 倾向于高效的开发会话

### 个人习惯
- **代码组织**: 喜欢清晰的模块划分和依赖关系
- **错误处理**: 重视错误处理和用户友好的提示
- **性能监控**: 关注实际使用中的性能表现
- **用户体验**: 从用户角度思考功能设计

---

**注意**: 这个文件应该根据实际的用户偏好和需求进行个性化调整。目前的内容基于项目特性和开发约束推测，请根据实际情况修改。