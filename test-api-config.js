// API密钥配置测试文件
// 在浏览器控制台中运行以下代码来测试配置

// 1. 获取Gemini配置实例
const gemini = window.moduleContainer ? window.moduleContainer.get('gemini') : null;

if (gemini) {
    console.log('🔍 开始测试Gemini API配置...');

    // 2. 检查API密钥是否已设置
    console.log('📋 API密钥状态:', gemini.isApiKeyValid() ? '✅ 已设置' : '❌ 未设置');

    // 3. 测试API连接 (使用默认模型)
    gemini.testConnection().then(result => {
        if (result.success) {
            console.log('🎉 API连接测试成功！');
        } else {
            console.error('❌ API连接测试失败:', result.message);
        }
    });

    // 4. 可选：测试gemini-2.5-flash模型
    console.log('🔄 正在测试gemini-2.0-flash-exp模型...');
    gemini.testConnection('gemini-2.0-flash-exp').then(result => {
        if (result.success) {
            console.log('🎉 gemini-2.0-flash-exp模型测试成功！');
        } else {
            console.error('❌ gemini-2.0-flash-exp模型测试失败:', result.message);
        }
    });

} else {
    console.error('❌ 无法获取Gemini配置实例，请检查模块是否正确加载');
}

// 手动测试API调用的示例
/*
const testPrompt = "请简单介绍一下你自己";
gemini.callGeminiAPIWithRetry(testPrompt, { model: 'gemini-2.0-flash-exp' })
    .then(result => {
        if (result.success) {
            console.log('🤖 AI响应:', result.data);
        } else {
            console.error('❌ API调用失败:', result.error);
        }
    });
*/
