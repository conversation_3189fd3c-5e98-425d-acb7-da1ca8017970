/**
 * ============================================================================
 * 提示词分割器模块 (prompt-segmenter.js) - 防AI幻觉核心声明
 * ============================================================================
 *
 * 【防AI幻觉声明】
 * 本文件是提示词文本的智能字段分割工具，基于模式匹配进行内容结构化。
 * 严禁AI基于文件名推测功能，必须完整阅读所有正则表达式和分割逻辑才能理解文本解析的完整机制。
 * 本文件不直接处理AI调用，仅提供文本处理和结构化功能。
 *
 * 【重复开发防护】
 * 警告：本项目已存在完整的提示词分割系统，严禁创建新的文本分割工具。
 * 任何新的文本解析需求必须在本文件中实现，而不是创建分散的解析逻辑。
 * 现有分割模式：[字段模式匹配 → 内容提取 → 结构重组] - 严禁修改此约定。
 *
 * 【竞态问题防护】
 * 文本处理竞态防护：
 * 1. 正则表达式状态重置：detectField方法中确保pattern.lastIndex重置
 * 2. 字符串操作原子化：segmentPrompt方法中的字符串分割和拼接序列化
 * 3. 状态变量保护：currentField和currentContent的更新按确定顺序
 * 防护措施：使用局部变量、确定性处理顺序、状态隔离确保处理的线程安全。
 *
 * 【声明与接口】
 * 导出：PromptSegmenter类、createPromptSegmenterModule工厂函数
 * 导入：依赖configManager模块容器服务
 * 数据结构：
 *   - fieldPatterns：字段识别的正则表达式映射
 *   - defaultFieldOrder：默认字段处理顺序
 * 主要接口：
 *   - segmentPrompt(fullPrompt, targetFields)：分割提示词为字段片段
 *   - recomposePrompt(segments, fieldOrder)：从片段重组提示词
 *   - analyzePromptStructure(fullPrompt)：分析提示词结构
 *   - createVisualEditor(fullPrompt)：创建可视化编辑器
 *
 * 【依赖关系网络】
 * 直接依赖：
 *   - configManager：配置管理器（用于字段模式配置）
 *
 * 被依赖关系：
 *   - 被main.js注册为提示词分割模块
 *   - 被promptComposer依赖进行片段组合前的分割
 *   - 被promptEditor依赖进行提示词分析和编辑
 *   - 被所有需要文本结构化的模块间接依赖
 *   - 影响整个应用的提示词处理质量
 *
 * 【加载时机与生命周期】
 * 加载时机：模块容器初始化阶段按依赖顺序加载
 * 初始化时机：构造函数中立即初始化字段模式和默认顺序
 * 生命周期：
 *   1. 文件加载 → 2. 工厂函数注册 → 3. 依赖注入 → 4. 字段模式初始化 → 5. 运行时文本分割 → 6. 结构分析 → 7. 可视化编辑 → 8. 持续可用（无清理）
 * 清理处理：静态数据，无需清理
 *
 * 【技术栈与环境要求】
 * 运行环境：浏览器环境（依赖window对象用于模块注册）
 * 技术栈：原生JavaScript ES6+，无框架依赖
 * 浏览器支持：现代浏览器（支持class语法、正则表达式、模板字符串）
 * 数据要求：包含完整的字段识别模式和处理规则
 *
 * 【核心功能说明】
 * 1. 智能字段分割：基于注释标记识别和提取字段内容
 * 2. 多格式支持：支持多种编程语言的注释格式
 * 3. 结构化重组：按指定顺序重新组合分割的片段
 * 4. 内容分析：分析提示词的结构特征和内容质量
 * 5. 可视化编辑：提供图形界面的字段编辑功能
 * 6. 批量处理：支持多个提示词文件的批量处理
 *
 * 【相关文件索引】
 * - main.js：负责本模块的注册
 * - prompt-composer.js：使用本分割器进行片段组合
 * - prompt-editor.js：使用本分割器进行提示词分析
 * - config.js：提供字段模式配置
 *
 * 【修改记录】
 * v1.0.0 (2025-08-31) - 初始版本
 *   - 实现完整的提示词字段分割功能
 *   - 添加多格式注释识别支持
 *   - 完善结构分析和重组逻辑
 *   - 添加详细的防AI幻觉注释系统
 *   - 实现可视化编辑器界面
 *
 * 【使用说明】
 * 文本分割：使用segmentPrompt方法将完整提示词分割为字段
 * 内容重组：使用recomposePrompt方法重新组合分割的片段
 * 结构分析：使用analyzePromptStructure方法分析提示词特征
 * 可视化编辑：使用createVisualEditor方法创建编辑界面
 *
 * ============================================================================
 */

/**
 * 提示词分割器模块 - 通用提示词按字段切割工具
 *
 * === 文件依赖关系网络 ===
 * 依赖项：无直接依赖（独立的文本处理工具）
 * 被依赖：prompt-composer.js（片段组合）, prompt-editor.js（片段分析）
 * 全局变量：创建 PromptSegmenter 实例（非全局）
 * 文本处理：正则表达式模式匹配、字段识别、内容分割
 *
 * === 核心功能 ===
 * - 提示词文本的智能字段分割
 * - 多语言注释标记识别（#、//、<!--、*）
 * - 字段内容提取和分类
 * - 分割结果的结构化输出
 * - 自定义字段模式配置
 *
 * === 集成点 ===
 * - prompt-composer.js：将完整提示词分割为字段片段
 * - prompt-editor.js：分析和编辑现有的提示词片段
 * - 可作为独立工具使用，无需其他依赖
 *
 * === 使用场景 ===
 * - 长提示词文本的智能分割和分类
 * - 多字段提示词模板的解析和处理
 * - 提示词片段的提取和重组
 * - 文本内容的模式识别和结构化
 *
 * === 注意事项 ===
 * 通用提示词按字段切割工具
 * 支持多种注释格式和语言标记
 * 可配置的字段识别模式
 * 轻量级工具，无外部依赖
 */

// 通用提示词按字段切割工具

class PromptSegmenter {
  constructor(configManager) {
    this.configManager = configManager;
    this.fieldPatterns = {
      // 基础信息字段
      base: /^(?:#|\/\/|<!--|\*)\s*基础模板|Base Template/i,
      validation: /^(?:#|\/\/|<!--|\*)\s*验证规则|Validation Rules/i,
      schema: /^(?:#|\/\/|<!--|\*)\s*输出格式|Output Schema/i,
      
      // 渠道相关字段
      ota: /^(?:#|\/\/|<!--|\*)\s*渠道标识|OTA Identification/i,
      ota_price: /^(?:#|\/\/|<!--|\*)\s*价格处理|Price Handling/i,
      
      // 客户信息字段
      customer: /^(?:#|\/\/|<!--|\*)\s*客户信息|Customer Information/i,
      customer_name: /^(?:#|\/\/|<!--|\*)\s*客户姓名|Customer Name/i,
      customer_contact: /^(?:#|\/\/|<!--|\*)\s*联系方式|Contact Information/i,
      customer_email: /^(?:#|\/\/|<!--|\*)\s*邮箱地址|Email Address/i,
      
      // 订单信息字段
      flight: /^(?:#|\/\/|<!--|\*)\s*航班信息|Flight Information/i,
      location: /^(?:#|\/\/|<!--|\*)\s*位置信息|Location Information/i,
      pickup: /^(?:#|\/\/|<!--|\*)\s*上车地点|Pickup Location/i,
      destination: /^(?:#|\/\/|<!--|\*)\s*目的地|Destination/i,
      
      // 服务需求字段
      requirements: /^(?:#|\/\/|<!--|\*)\s*特殊需求|Special Requirements/i,
      passenger_number: /^(?:#|\/\/|<!--|\*)\s*乘客数量|Passenger Count/i,
      luggage_number: /^(?:#|\/\/|<!--|\*)\s*行李数量|Luggage Count/i,
      
      // 其他字段
      extra: /^(?:#|\/\/|<!--|\*)\s*额外说明|Additional Notes/i,
      format: /^(?:#|\/\/|<!--|\*)\s*格式要求|Format Requirements/i
    };
    
    this.defaultFieldOrder = [
      'base', 'validation', 'schema', 
      'ota', 'ota_price',
      'customer', 'customer_name', 'customer_contact', 'customer_email',
      'flight', 'location', 'pickup', 'destination',
      'requirements', 'passenger_number', 'luggage_number',
      'extra', 'format'
    ];
  }

  /**
   * 将通用提示词按字段切割
   */
  segmentPrompt(fullPrompt, targetFields = null) {
    const lines = fullPrompt.split('\n');
    const segments = {};
    let currentField = null;
    let currentContent = [];
    
    for (const line of lines) {
      const fieldMatch = this.detectField(line);
      
      if (fieldMatch) {
        // 保存前一个字段的内容
        if (currentField && currentContent.length > 0) {
          segments[currentField] = currentContent.join('\n').trim();
        }
        
        // 开始新字段
        currentField = fieldMatch;
        currentContent = [line];
      } else if (currentField) {
        // 继续当前字段
        currentContent.push(line);
      } else {
        // 没有字段标识的内容，放到base字段
        if (!segments.base) {
          segments.base = [];
        }
        segments.base.push(line);
      }
    }
    
    // 保存最后一个字段
    if (currentField && currentContent.length > 0) {
      segments[currentField] = currentContent.join('\n').trim();
    }
    
    // 处理base字段
    if (segments.base && Array.isArray(segments.base)) {
      segments.base = segments.base.join('\n').trim();
    }
    
    // 过滤字段（如果指定了目标字段）
    if (targetFields) {
      const filteredSegments = {};
      for (const field of targetFields) {
        if (segments[field]) {
          filteredSegments[field] = segments[field];
        }
      }
      return filteredSegments;
    }
    
    return segments;
  }

  /**
   * 检测字段标识
   */
  detectField(line) {
    for (const [field, pattern] of Object.entries(this.fieldPatterns)) {
      if (pattern.test(line)) {
        return field;
      }
    }
    return null;
  }

  /**
   * 从片段重组提示词
   */
  recomposePrompt(segments, fieldOrder = null) {
    const order = fieldOrder || this.defaultFieldOrder;
    const lines = [];
    
    // 按指定顺序添加字段
    for (const field of order) {
      if (segments[field]) {
        lines.push(segments[field]);
        lines.push(''); // 空行分隔
      }
    }
    
    // 添加未排序的字段
    for (const [field, content] of Object.entries(segments)) {
      if (!order.includes(field)) {
        lines.push(content);
        lines.push('');
      }
    }
    
    return lines.join('\n').trim();
  }

  /**
   * 自动识别并提取字段结构
   */
  analyzePromptStructure(fullPrompt) {
    const segments = this.segmentPrompt(fullPrompt);
    const structure = [];
    
    for (const [field, content] of Object.entries(segments)) {
      structure.push({
        field: field,
        length: content.length,
        lineCount: content.split('\n').length,
        hasPlaceholders: this.hasPlaceholders(content),
        hasJson: this.hasJsonContent(content)
      });
    }
    
    return {
      totalFields: Object.keys(segments).length,
      totalLength: fullPrompt.length,
      structure: structure.sort((a, b) => b.length - a.length),
      segments: segments
    };
  }

  /**
   * 检查内容是否包含占位符
   */
  hasPlaceholders(content) {
    return /\{[^}]+\}/.test(content);
  }

  /**
   * 检查内容是否包含JSON
   */
  hasJsonContent(content) {
    return content.trim().startsWith('{') && content.trim().endsWith('}');
  }

  /**
   * 生成字段模板
   */
  generateFieldTemplate(field, exampleContent = '') {
    const templates = {
      base: `# 基础模板\n通用订单解析提示词，包含基本指令和格式要求。\n\n${exampleContent || '请严格按照输出格式要求返回JSON，字段齐全，即使为null也要包含。'}`,
      
      validation: `# 验证规则\n数据验证规则和格式要求。\n\n${exampleContent || '日期格式：YYYY-MM-DD，时间格式：HH:MM，phone允许+和分隔符。'}`,
      
      schema: `# 输出格式\n严格的JSON输出格式要求。\n\n${exampleContent || '请严格输出与API字段需求一致的JSON，仅输出纯JSON，不要包含任何解释性文字。'}`,
      
      ota: `# 渠道标识\n特定渠道的识别和处理规则。\n\n${exampleContent || '渠道专属处理：\n- 渠道标识：渠道名称\n- 特殊处理规则说明'}`,
      
      ota_price: `# 价格处理\n价格字段的特殊处理规则。\n\n${exampleContent || '价格字段特殊处理：\n- 货币类型\n- 换算公式\n- 输出格式要求'}`
    };
    
    return templates[field] || `# ${field}\n${exampleContent || '字段内容描述'}`;
  }

  /**
   * 批量处理提示词文件
   */
  async processPromptFile(fileContent, options = {}) {
    const {
      targetFields = null,
      fieldOrder = null,
      includeDefaults = false
    } = options;
    
    const segments = this.segmentPrompt(fileContent, targetFields);
    
    if (includeDefaults) {
      // 为缺失的字段添加默认模板
      for (const field of this.defaultFieldOrder) {
        if (!segments[field] && field !== 'base') {
          segments[field] = this.generateFieldTemplate(field);
        }
      }
    }
    
    return {
      segments: segments,
      recomposed: this.recomposePrompt(segments, fieldOrder),
      analysis: this.analyzePromptStructure(fileContent)
    };
  }

  /**
   * 创建可视化编辑器界面
   */
  createVisualEditor(fullPrompt = '') {
    const analysis = this.analyzePromptStructure(fullPrompt);
    
    return `
      <div style="font-family: Arial, sans-serif;">
        <h2>提示词字段分析器</h2>
        
        <div style="display: grid; grid-template-columns: 300px 1fr; gap: 20px;">
          <!-- 字段列表 -->
          <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
            <h3>检测到的字段 (${analysis.totalFields})</h3>
            <div style="max-height: 400px; overflow-y: auto;">
              ${analysis.structure.map(item => `
                <div style="
                  padding: 8px; margin-bottom: 5px; 
                  background: white; border: 1px solid #dee2e6; 
                  border-radius: 4px; cursor: pointer;
                " onclick="selectField('${item.field}')">
                  <strong>${item.field}</strong>
                  <div style="font-size: 12px; color: #6c757d;">
                    ${item.length}字符, ${item.lineCount}行
                    ${item.hasPlaceholders ? '📍' : ''}
                    ${item.hasJson ? '📋' : ''}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          
          <!-- 字段内容编辑 -->
          <div>
            <h3>字段内容编辑</h3>
            <textarea 
              id="field-editor" 
              style="width: 100%; height: 300px; font-family: monospace; padding: 10px;"
              placeholder="选择左侧字段进行编辑...">
            </textarea>
            
            <div style="margin-top: 15px;">
              <button onclick="saveField()" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px;">
                保存字段
              </button>
              <button onclick="addNewField()" style="padding: 8px 16px; background: #17a2b8; color: white; border: none; border-radius: 4px; margin-left: 10px;">
                添加新字段
              </button>
            </div>
          </div>
        </div>
        
        <!-- 预览 -->
        <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 6px;">
          <h3>重组预览</h3>
          <pre style="background: white; padding: 15px; border-radius: 4px; max-height: 200px; overflow-y: auto;">
            ${this.recomposePrompt(analysis.segments)}
          </pre>
        </div>
      </div>
    `;
  }
}

// 模块工厂函数
function createPromptSegmenterModule(container) {
  const configManager = container.get('config'); // Assuming config is needed for field patterns
  return new PromptSegmenter(configManager);
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
  window.registerModule('promptSegmenter', createPromptSegmenterModule, ['config']);
  console.log('📦 PromptSegmenter 已注册到模块容器');
}