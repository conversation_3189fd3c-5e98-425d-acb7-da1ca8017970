/**
 * ============================================================================
 * 字段映射器模块 (field-mapper.js) - 防AI幻觉核心声明
 * ============================================================================
 *
 * 【防AI幻觉声明】
 * 本文件是字段映射和数据转换的统一管理中心，负责将非结构化文本转换为结构化API字段。
 * 严禁AI基于文件名推测功能，必须完整阅读所有字段验证、AI增强和地址翻译逻辑才能理解数据映射机制的完整性。
 * 本文件不直接处理业务逻辑，仅提供数据转换和验证服务。
 *
 * 【重复开发防护】
 * 警告：本项目已存在完整的字段映射系统，严禁创建新的字段映射模块。
 * 任何新的数据映射需求必须通过本工具实现，而不是创建分散的映射逻辑。
 * 现有字段映射模式：[文本输入 → 渠道检测 → AI增强 → 字段验证 → 地址翻译 → 数据输出] - 严禁修改此约定。
 *
 * 【竞态问题防护】
 * 字段映射竞态防护：
 * 1. 异步AI调用序列化：enhanceWithGemini方法中的Gemini API调用按顺序执行
 * 2. 地址翻译同步：translateAddresses方法中的pickup和destination翻译按顺序执行
 * 3. 数据验证原子化：validateGeminiJson方法中的字段验证同步执行
 * 4. 依赖注入保护：构造函数中的服务依赖检查防止运行时错误
 * 防护措施：使用async/await序列化、依赖检查、原子性验证确保数据处理的可靠性。
 *
 * 【声明与接口】
 * 导出：FieldMapper类、createFieldMapperModule工厂函数
 * 导入：依赖注入的geminiService、channelDetector、addressTranslator、promptFragmentManager
 * 主要接口：
 *   - validateGeminiJson(obj)：验证和规范化Gemini返回的JSON数据
 *   - extractJsonSubstring(s)：从字符串中提取JSON子串
 *   - enhanceWithGemini(text, detectedChannel, customPrompt)：使用AI增强字段提取
 *   - getApiFieldDefinitions()：获取API字段定义
 *   - processCompleteData(text, customPrompt)：完整的数据处理流程
 *   - safeTranslateAddresses(data)：安全执行地址翻译
 *   - translateAddresses(data)：执行地址翻译
 *
 * 【依赖关系网络】
 * 直接依赖：
 *   - geminiService：AI服务用于字段增强 @INJECTED
 *   - channelDetector：渠道检测服务 @INJECTED
 *   - addressTranslator：地址翻译服务 @INJECTED
 *   - promptFragmentManager：提示词管理服务 @INJECTED
 *
 * 被依赖关系：
 *   - 被main.js注册为字段映射模块
 *   - 被所有需要数据转换的业务模块间接依赖
 *   - 被prompt-editor.js依赖用于数据处理
 *   - 被channel-detector.js依赖用于字段验证
 *   - 影响整个应用的数据处理和API集成
 *
 * 【加载时机与生命周期】
 * 加载时机：模块容器初始化阶段按依赖顺序加载
 * 初始化时机：构造函数中接收依赖注入的服务实例
 * 生命周期：
 *   1. 文件加载 → 2. 工厂函数注册 → 3. 依赖解析 → 4. 实例创建 → 5. 字段定义初始化 → 6. 运行时数据处理 → 7. AI增强调用 → 8. 地址翻译 → 9. 数据验证 → 10. 结果返回 → 11. 实例销毁
 * 清理处理：无特殊清理需求（无持久化状态）
 *
 * 【技术栈与环境要求】
 * 运行环境：浏览器环境（依赖async/await和ES6+）
 * 技术栈：原生JavaScript ES6+，依赖注入模式
 * 浏览器支持：现代浏览器（支持async/await、Promise、JSON.parse）
 * 数据处理：支持JSON验证、字符串处理、正则表达式
 *
 * 【核心功能说明】
 * 1. 数据验证：validateGeminiJson方法验证和规范化字段数据
 * 2. AI增强：enhanceWithGemini方法使用Gemini AI进行智能字段提取
 * 3. 地址翻译：translateAddresses方法将中文地址翻译为英文
 * 4. 字段定义：getApiFieldDefinitions方法提供API字段规范
 * 5. 完整处理：processCompleteData方法整合所有处理步骤
 * 6. 错误处理：safeTranslateAddresses方法提供安全的地址翻译
 * 7. 依赖注入：构造函数接收所有必需的服务依赖
 *
 * 【相关文件索引】
 * - main.js：负责本模块的注册和依赖注入
 * - gemini-config.js：提供Gemini AI服务
 * - channel-detector.js：提供渠道检测服务
 * - address-translator.js：提供地址翻译服务
 * - prompt-fragment-manager.js：提供提示词管理服务
 * - prompt-editor.js：使用本模块进行数据处理
 *
 * 【修改记录】
 * v2.0.0 (2025-08-31) - 模块化重构版本
 *   - 消除全局变量污染，采用依赖注入模式
 *   - 分离关注点，专注于字段映射逻辑
 *   - 保持AI增强处理的核心功能不变
 *   - 添加详细的防AI幻觉注释系统
 *   - 实现服务解耦和错误隔离机制
 *
 * 【使用说明】
 * 数据验证：使用validateGeminiJson验证字段数据的有效性
 * AI增强：使用enhanceWithGemini进行智能字段提取
 * 地址翻译：使用translateAddresses翻译中文地址
 * 完整处理：使用processCompleteData执行完整的数据处理流程
 * 字段定义：使用getApiFieldDefinitions获取API字段规范
 * 依赖注入：通过构造函数注入所有必需的服务依赖
 *
 * ============================================================================
 */

/**
 * 字段映射器模块 - 模块化重构版本
 *
 * 重构更新：
 * - 消除全局变量污染 (window.fieldMapper)
 * - 实现依赖注入，显式传入Gemini服务和其他依赖
 * - 分离关注点：专注于字段映射逻辑，服务定位交给容器
 * - 保持AI增强处理的核心功能不变
 *
 * 设计原则：
 * - 依赖注入：通过构造函数接收所需服务
 * - 服务解耦：不直接依赖全局服务实例
 * - 功能稳定：保持原有API和处理逻辑
 * - 错误隔离：依赖不可用时优雅降级
 */

/**
 * 字段映射器模块 - 模块化重构版本
 *
 * 重构更新：
 * - 消除全局变量污染 (window.fieldMapper)
 * - 实现依赖注入，显式传入Gemini服务和其他依赖
 * - 分离关注点：专注于字段映射逻辑，服务定位交给容器
 * - 保持AI增强处理的核心功能不变
 *
 * 设计原则：
 * - 依赖注入：通过构造函数接收所需服务
 * - 服务解耦：不直接依赖全局服务实例
 * - 功能稳定：保持原有API和处理逻辑
 * - 错误隔离：依赖不可用时优雅降级
 */

/**
 * 字段映射器模块 - 模块化重构版本
 * 
 * 重构更新：
 * - 消除全局变量污染 (window.fieldMapper)
 * - 实现依赖注入，显式传入Gemini服务和其他依赖
 * - 分离关注点：专注于字段映射逻辑，服务定位交给容器
 * - 保持AI增强处理的核心功能不变
 * 
 * 设计原则：
 * - 依赖注入：通过构造函数接收所需服务
 * - 服务解耦：不直接依赖全局服务实例
 * - 功能稳定：保持原有API和处理逻辑
 * - 错误隔离：依赖不可用时优雅降级
 */

class FieldMapper {
    constructor(geminiService = null, channelDetector = null, addressTranslator = null, promptFragmentManager = null) {
        // 依赖注入 - 接收外部服务
        this.geminiService = geminiService;
        this.channelDetector = channelDetector; 
        this.addressTranslator = addressTranslator;
        this.promptFragmentManager = promptFragmentManager;
        
        this.defaultValues = {};
        this.apiFieldDefinitions = {
            descriptions: {
                customer_name: '客户姓名',
                customer_contact: '客户联系电话',
                customer_email: '客户邮箱',
                ota_reference_number: 'OTA参考编号',
                flight_info: '航班信息',
                pickup: '接客地点',
                destination: '目的地',
                passenger_number: '乘客数量',
                luggage_number: '行李数量',
                ota_price: 'OTA平台价格',
                sub_category_id: '服务类型ID',
                extra_requirement: '额外要求/备注信息',
                date: '服务日期',
                time: '服务时间',
                car_type_id: '车型ID',
                languages_id_array: '语言服务数组',
                ota: '渠道名称'
            }
        };
    }

    

    validateGeminiJson(obj) {
        const errors = [];
        const normalized = { ...obj };
        const toNum = v => {
            if (v === null || v === undefined || v === '') return null;
            const n = Number(v);
            return Number.isFinite(n) ? n : null;
        };

        ['car_type_id', 'sub_category_id', 'passenger_number', 'luggage_number', 'ota_price'].forEach(k => {
            normalized[k] = normalized.hasOwnProperty(k) ? toNum(normalized[k]) : null;
        });

        ['ota', 'ota_reference_number', 'customer_name', 'customer_contact', 'customer_email', 'flight_info', 'pickup', 'destination', 'date', 'time', 'extra_requirement', 'extra_requirement_raw'].forEach(k => {
            if (!normalized.hasOwnProperty(k) || normalized[k] === undefined) normalized[k] = null;
            else if (normalized[k] !== null && typeof normalized[k] !== 'string') normalized[k] = String(normalized[k]);
        });

        if (!normalized.hasOwnProperty('languages_id_array') || normalized.languages_id_array === undefined) normalized.languages_id_array = null;
        else if (normalized.languages_id_array !== null && typeof normalized.languages_id_array === 'object') {
            const out = {}; let i = 0;
            Object.values(normalized.languages_id_array).forEach(v => { out[String(i++)] = toNum(v); });
            normalized.languages_id_array = out;
        } else if (normalized.languages_id_array !== null) {
            errors.push('languages_id_array must be object or null'); normalized.languages_id_array = null;
        }

        if (normalized.extra_requirement && normalized.extra_requirement.length > 120) normalized.extra_requirement = normalized.extra_requirement.substring(0, 120);
        if (normalized.ota === '') normalized.ota = null;

        return { valid: errors.length === 0, data: normalized, errors };
    }

    extractJsonSubstring(s) {
        const first = s.indexOf('{'); const last = s.lastIndexOf('}');
        if (first === -1 || last === -1 || last <= first) return null; return s.substring(first, last+1);
    }

    async enhanceWithGemini(text, detectedChannel, customPrompt = null) {
        // 如果提供了自定义提示词，直接使用它
        if (customPrompt) {
            try {
                if (this.geminiService && typeof this.geminiService.callGeminiAPIWithRetry === 'function') {
                    const result = await this.geminiService.callGeminiAPIWithRetry(customPrompt);
                    if (result.success && result.content) {
                        const json = this.extractJsonSubstring(String(result.content));
                        if (!json) return { __raw: result.content };
                        const parsed = JSON.parse(json);
                        const v = this.validateGeminiJson(parsed);
                        return v.valid ? v.data : { __raw: result.content };
                    } else {
                        console.warn('Gemini API调用失败:', result.error);
                        return {};
                    }
                } else {
                    console.warn('Gemini配置不可用，无法进行AI增强');
                    return {};
                }
            } catch (e) {
                console.warn('Gemini增强处理失败:', e.message);
                return {};
            }
        }

        // 优先使用注入的 promptFragmentManager
        if (!this.promptFragmentManager) {
            console.error('❌ PromptFragmentManager 服务不可用!');
            throw new Error('PromptFragmentManager service is not available.');
        }

        const prompt = this.promptFragmentManager.buildPrompt(text, detectedChannel);
        
        try {
            // 使用注入的Gemini服务
            if (this.geminiService && typeof this.geminiService.callGeminiAPIWithRetry === 'function') {
                const result = await this.geminiService.callGeminiAPIWithRetry(prompt);
                if (result.success && result.content) {
                    const json = this.extractJsonSubstring(String(result.content));
                    if (!json) return { __raw: result.content };
                    const parsed = JSON.parse(json);
                    const v = this.validateGeminiJson(parsed);
                    return v.valid ? v.data : { __raw: result.content };
                } else {
                    console.warn('Gemini API调用失败:', result.error);
                    return {};
                }
            } else {
                console.warn('Gemini配置不可用，无法进行AI增强');
                return {};
            }
        } catch (e) {
            console.warn('Gemini增强处理失败:', e.message);
            return {};
        }
    }
    
    

    
    
    /**
     * 获取API字段定义
     * @returns {Object} 字段定义对象
     */
    getApiFieldDefinitions() {
        return this.apiFieldDefinitions;
    }
    
    async processCompleteData(text, customPrompt = null) {
        try {
            // 步骤1: 渠道检测 (前置处理，为提示词提供上下文)
            let detectedChannel = null;
            try {
                if (this.channelDetector && typeof this.channelDetector.detectChannel === 'function') {
                    const cd = this.channelDetector.detectChannel(text);
                    if (cd && cd.channel && cd.confidence > 0.3) {
                        detectedChannel = cd.channel;
                    }
                }
            } catch (e) {
                console.warn('渠道检测失败:', e.message);
            }

            // 步骤2: 使用新的PromptFragmentManager和Gemini进行智能提取
            const gemini = customPrompt
                ? await this.enhanceWithGemini(text, detectedChannel, customPrompt)
                : await this.enhanceWithGemini(text, detectedChannel) || {};
            
            // 如果 Gemini 返回了原始内容，说明处理失败
            if (gemini.__raw) {
                return {
                    data: {},
                    geminiUsed: false,
                    validation: { isValid: false, message: 'Gemini 处理失败', missingFields: [] },
                    extractedFields: [],
                    enhancedFields: [],
                    addressTranslationUsed: false,
                    translationResults: {},
                    error: 'Gemini processing failed'
                };
            }
            
            // 步骤3: 数据验证和地址翻译
            const validationResult = this.validateGeminiJson(gemini);
            const translated = await this.safeTranslateAddresses(validationResult.data);
            
            // 创建验证信息
            const validation = {
                isValid: validationResult?.valid || false,
                message: validationResult?.valid ? '所有字段验证通过' : '字段验证发现问题',
                missingFields: Array.isArray(validationResult?.errors) ? validationResult.errors : []
            };
            
            return { 
                data: translated.data || validationResult.data || {},
                geminiUsed: true,
                validation: validation,
                extractedFields: [], // 不再使用本地提取
                enhancedFields: gemini ? Object.keys(gemini).filter(k => k !== '__raw') : [],
                addressTranslationUsed: translated.addressTranslationUsed || false,
                translationResults: translated.translationResults || {}
            };
        } catch (error) {
            console.error('processCompleteData 发生错误:', error);
            // 返回安全的默认结构
            return {
                data: {},
                geminiUsed: false,
                validation: { isValid: false, message: `处理失败: ${error.message}`, missingFields: [] },
                extractedFields: [],
                enhancedFields: [],
                addressTranslationUsed: false,
                translationResults: {},
                error: error.message
            };
        }
    }

    async safeTranslateAddresses(data) {
        try {
            return await this.translateAddresses(data);
        } catch (error) {
            console.warn('地址翻译失败:', error);
            return { data: data || {}, addressTranslationUsed: false, translationResults: {} };
        }
    }

    async translateAddresses(data) {
        if (!data) return { data };
        
        const translator = this.addressTranslator;
        
        if (!translator) {
            return { data };
        }
        
        const out = { ...data };
        const translationResults = {};
        let hasTranslations = false;
        
        try {
            // 翻译pickup地址
            if (out.pickup) {
                const pickupResult = await translator.translateAddress(out.pickup);
                translationResults.pickup = pickupResult;
                
                if (pickupResult && pickupResult.translated && pickupResult.confidence > 0.7) {
                    out.pickup = pickupResult.translated;
                    out.pickup_original = data.pickup;
                    hasTranslations = true;
                }
            }
            
            // 翻译destination地址
            if (out.destination) {
                const destinationResult = await translator.translateAddress(out.destination);
                translationResults.destination = destinationResult;
                
                if (destinationResult && destinationResult.translated && destinationResult.confidence > 0.7) {
                    out.destination = destinationResult.translated;
                    out.destination_original = data.destination;
                    hasTranslations = true;
                }
            }
        } catch (e) {
            console.warn('地址翻译失败:', e.message);
        }
        
        return { 
            data: out,
            translationResults: hasTranslations ? translationResults : {},
            addressTranslationUsed: hasTranslations
        };
    }
}

// 模块工厂函数 - Refactored
function createFieldMapperModule(container) {
    const geminiService = container.get('gemini');
    const channelDetector = container.get('channelDetector');
    const addressTranslator = container.get('addressTranslator');
    const promptFragmentManager = container.get('promptFragmentManager');
    
    return new FieldMapper(geminiService, channelDetector, addressTranslator, promptFragmentManager);
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('fieldMapper', createFieldMapperModule, ['gemini', 'channelDetector', 'addressTranslator', 'promptFragmentManager']);
    console.log('📦 FieldMapper已注册到模块容器');
}
