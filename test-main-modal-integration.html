<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Main-Modal协同功能测试</title>
    <link rel="stylesheet" href="styles/main.css">
    <style>
        .test-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .test-status {
            background: #f8f9fa;
            border-left: 4px solid #17a2b8;
            padding: 10px 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .test-error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .test-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        #testOrderContent {
            width: 100%;
            min-height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Main-Modal协同功能测试</h1>
        <p>这个页面用于测试PromptProcessor和PromptManageModal的深度关联功能。</p>
        
        <div class="test-section">
            <h3>📋 测试订单内容</h3>
            <textarea id="testOrderContent" placeholder="输入测试订单内容...">
订单号：202401150001
客户姓名：张三
联系电话：138****1234
出行时间：2024-01-20 10:00
接客地点：首都机场T3航站楼
目的地：北京朝阳区国贸CBD
乘客人数：2人
行李数量：3件
价格：268元
备注：请准时到达，有重要会议
            </textarea>
            <div class="test-controls">
                <button class="btn btn-primary" onclick="testProcessOrder()">🚀 测试订单处理</button>
                <button class="btn btn-secondary" onclick="clearTestResults()">🧹 清空结果</button>
                <button class="btn btn-purple" onclick="testOpenModal()">🎛️ 测试打开Modal</button>
                <button class="btn btn-orange" onclick="testViewMapping()">🎯 测试映射显示</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 协同组件状态</h3>
            <div id="componentStatus">
                <div class="test-status">正在检查组件状态...</div>
            </div>
            <div class="test-controls">
                <button class="btn btn-secondary" onclick="checkComponentStatus()">🔍 检查组件状态</button>
                <button class="btn btn-purple" onclick="testCoordinatorSync()">🔄 测试协同同步</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📈 测试结果</h3>
            <div id="testResults">
                <div class="test-status">等待测试执行...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔧 调试控制台</h3>
            <div class="test-status">打开浏览器开发者工具查看详细调试信息</div>
            <div class="test-controls">
                <button class="btn btn-secondary" onclick="showDebugInfo()">📋 显示调试信息</button>
                <button class="btn btn-danger" onclick="simulateError()">❌ 模拟错误</button>
            </div>
        </div>
    </div>
    
    <!-- 加载所有必需的脚本 -->
    <script src="js/core/module-container.js"></script>
    <script src="js/core/error-handler.js"></script>
    <script src="js/utils/local-storage-manager.js"></script>
    <script src="js/utils/crypto-utils.js"></script>
    <script src="data/hotels_by_region.js"></script>
    <script src="js/config/airport-data.js"></script>
    <script src="js/config/config.js"></script>
    <script src="js/services/gemini-config.js"></script>
    <script src="js/business/channel-detector.js"></script>
    <script src="js/services/address-translator.js"></script>
    <script src="js/business/channel-data-manager.js"></script>
    <script src="js/business/prompt-segmenter.js"></script>
    <script src="js/business/prompt-composer.js"></script>
    <script src="js/business/prompt-fragments.js"></script>
    <script src="js/business/field-mapper.js"></script>
    <script src="js/business/prompt-processor.js"></script>
    <script src="js/core/main-modal-coordinator.js"></script>
    <script src="js/ui/prompt-manage-modal.js"></script>
    <script src="js/services/cache-manager.js"></script>
    <script src="js/services/cache-integration-adapter.js"></script>
    <script src="js/ui/cache-monitor-panel.js"></script>
    <script src="js/business/app.js"></script>
    <script src="js/core/main.js"></script>
    
    <script>
        // 测试变量
        let testProcessor, testModal, testCoordinator;
        
        // 等待模块加载完成
        setTimeout(() => {
            initializeTestComponents();
        }, 2000);
        
        function initializeTestComponents() {
            try {
                console.log('🔧 初始化测试组件...');
                
                // 获取组件实例
                testProcessor = window.moduleContainer?.get('promptProcessor');
                testModal = window.moduleContainer?.get('promptManageModal');
                testCoordinator = window.moduleContainer?.get('mainModalCoordinator');
                
                // 建立关联
                if (testCoordinator && testProcessor && testModal) {
                    testCoordinator.linkComponents(testProcessor, testModal);
                    updateStatus('✅ 协同组件初始化成功');
                } else {
                    updateStatus('⚠️ 部分组件初始化失败', 'error');
                }
                
                checkComponentStatus();
                
            } catch (error) {
                console.error('❌ 初始化测试组件失败:', error);
                updateStatus('❌ 初始化失败: ' + error.message, 'error');
            }
        }
        
        async function testProcessOrder() {
            const content = document.getElementById('testOrderContent').value.trim();
            if (!content) {
                updateResults('请输入测试内容', 'error');
                return;
            }
            
            if (!testProcessor) {
                updateResults('PromptProcessor未初始化', 'error');
                return;
            }
            
            try {
                updateResults('🔄 开始处理订单内容...');
                
                const result = await testProcessor.processOrderContent(content);
                
                if (result.success) {
                    updateResults('✅ 订单处理成功', 'success');
                    console.log('处理结果:', result);
                    
                    // 显示字段映射
                    const fieldMapping = testProcessor.getFieldToFragmentMapping();
                    if (fieldMapping && Object.keys(fieldMapping).length > 0) {
                        updateResults('🎯 字段映射生成完成，包含 ' + Object.keys(fieldMapping).length + ' 个字段', 'success');
                    }
                } else {
                    updateResults('❌ 处理失败: ' + result.error, 'error');
                }
                
            } catch (error) {
                console.error('测试处理失败:', error);
                updateResults('❌ 测试失败: ' + error.message, 'error');
            }
        }
        
        function testOpenModal() {
            if (!testModal) {
                updateResults('PromptManageModal未初始化', 'error');
                return;
            }
            
            try {
                updateResults('🎛️ 测试渠道同步顺序: 规则→渠道列表→片段提示词');
                updateResults('🎛️ 打开提示词管理模态框...');
                testModal.openModal({
                    channel: testProcessor?.lastDetectedChannel?.channel || 'generic'
                });
                updateResults('✅ Modal打开成功 - 验证渠道同步顺序', 'success');
                updateResults('📋 检查控制台查看渠道列表同步日志', 'success');
                
            } catch (error) {
                console.error('打开Modal失败:', error);
                updateResults('❌ 打开Modal失败: ' + error.message, 'error');
            }
        }
        
        function testViewMapping() {
            if (!testProcessor) {
                updateResults('PromptProcessor未初始化', 'error');
                return;
            }
            
            const fieldMapping = testProcessor.getFieldToFragmentMapping();
            if (!fieldMapping || Object.keys(fieldMapping).length === 0) {
                updateResults('⚠️ 暂无字段映射数据，请先处理订单内容', 'error');
                return;
            }
            
            try {
                updateResults('🎯 显示字段映射...');
                console.log('字段映射数据:', fieldMapping);
                
                const mappingCount = Object.keys(fieldMapping).length;
                const mappedCount = Object.values(fieldMapping).filter(m => m.hasMapping).length;
                
                updateResults(`✅ 字段映射显示完成：共${mappingCount}个字段，其中${mappedCount}个有映射片段`, 'success');
                
            } catch (error) {
                console.error('显示映射失败:', error);
                updateResults('❌ 显示映射失败: ' + error.message, 'error');
            }
        }
        
        function checkComponentStatus() {
            const status = [];
            
            // 检查模块容器
            const containerStatus = window.moduleContainer ? '✅ 已加载' : '❌ 未加载';
            status.push(`模块容器: ${containerStatus}`);
            
            // 检查各组件
            const processorStatus = testProcessor ? '✅ 已初始化' : '❌ 未初始化';
            status.push(`PromptProcessor: ${processorStatus}`);
            
            const modalStatus = testModal ? '✅ 已初始化' : '❌ 未初始化';
            status.push(`PromptManageModal: ${modalStatus}`);
            
            const coordinatorStatus = testCoordinator ? '✅ 已初始化' : '❌ 未初始化';
            status.push(`MainModalCoordinator: ${coordinatorStatus}`);
            
            // 检查关联状态
            if (testCoordinator) {
                const coordinatorInfo = testCoordinator.getCoordinatorStatus();
                status.push(`协同状态: ${coordinatorInfo.isLinked ? '✅ 已关联' : '❌ 未关联'}`);
                status.push(`实时模式: ${coordinatorInfo.isRealTimeMode ? '✅ 启用' : '❌ 禁用'}`);
            }
            
            updateStatus(status.join('<br>'));
        }
        
        function testCoordinatorSync() {
            if (!testCoordinator) {
                updateResults('MainModalCoordinator未初始化', 'error');
                return;
            }
            
            try {
                updateResults('🔄 测试协同同步...');
                testCoordinator.manualSync();
                updateResults('✅ 协同同步测试完成', 'success');
                
            } catch (error) {
                console.error('协同同步测试失败:', error);
                updateResults('❌ 协同同步测试失败: ' + error.message, 'error');
            }
        }
        
        function showDebugInfo() {
            const debugInfo = {
                moduleContainer: !!window.moduleContainer,
                promptProcessor: !!testProcessor,
                promptManageModal: !!testModal,
                mainModalCoordinator: !!testCoordinator,
                coordinatorStatus: testCoordinator ? testCoordinator.getCoordinatorStatus() : null,
                processorStats: testProcessor ? testProcessor.getProcessingStats() : null
            };
            
            console.log('🔧 调试信息:', debugInfo);
            updateResults('📋 调试信息已输出到控制台', 'success');
        }
        
        function simulateError() {
            try {
                throw new Error('这是一个模拟错误，用于测试错误处理机制');
            } catch (error) {
                console.error('模拟错误:', error);
                updateResults('❌ 模拟错误: ' + error.message, 'error');
            }
        }
        
        function clearTestResults() {
            updateResults('测试结果已清空');
            updateStatus('组件状态已重置');
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('componentStatus');
            statusDiv.innerHTML = `<div class="test-status ${type === 'error' ? 'test-error' : type === 'success' ? 'test-success' : ''}">${message}</div>`;
        }
        
        function updateResults(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const resultClass = type === 'error' ? 'test-error' : type === 'success' ? 'test-success' : '';
            resultsDiv.innerHTML += `<div class="test-status ${resultClass}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // 监听协同事件
        window.addEventListener('main-modal-coordinator', (event) => {
            console.log('协同事件:', event.detail);
            updateResults(`📡 协同事件: ${event.detail.type}`, 'success');
        });
        
        window.addEventListener('prompt-processor-update', (event) => {
            console.log('Processor事件:', event.detail);
            updateResults(`🔄 Processor事件: ${event.detail.type}`, 'success');
        });
        
        window.addEventListener('prompt-modal-event', (event) => {
            console.log('Modal事件:', event.detail);
            updateResults(`🎛️ Modal事件: ${event.detail.type}`, 'success');
        });
    </script>
</body>
</html>