<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存系统性能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .main-content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .test-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }

        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: black;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .result-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .result-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-value {
            font-weight: 600;
            color: #4facfe;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-healthy { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }

        .test-log {
            background: #1a1a1a;
            color: #e5e5e5;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .test-data {
            margin-top: 20px;
        }

        .test-data textarea {
            width: 100%;
            min-height: 150px;
            padding: 12px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            font-family: monospace;
            font-size: 13px;
            resize: vertical;
        }

        @media (max-width: 768px) {
            .test-controls {
                flex-direction: column;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 缓存系统性能测试</h1>
            <p>验证缓存效果和性能提升 - 支持Gemini API、地址翻译和渠道检测缓存</p>
        </div>

        <div class="main-content">
            <!-- 缓存状态概览 -->
            <div class="test-section">
                <h2>📊 缓存系统状态</h2>
                <div class="results-grid">
                    <div class="result-card" id="cache-status">
                        <h3>系统状态</h3>
                        <div class="metric">
                            <span>缓存管理器</span>
                            <span class="metric-value" id="cache-manager-status">
                                <span class="status-indicator status-error"></span>未连接
                            </span>
                        </div>
                        <div class="metric">
                            <span>集成适配器</span>
                            <span class="metric-value" id="adapter-status">
                                <span class="status-indicator status-error"></span>未连接
                            </span>
                        </div>
                        <div class="metric">
                            <span>监控面板</span>
                            <span class="metric-value" id="monitor-status">
                                <span class="status-indicator status-error"></span>未连接
                            </span>
                        </div>
                    </div>

                    <div class="result-card" id="cache-stats">
                        <h3>缓存统计</h3>
                        <div class="metric">
                            <span>总体命中率</span>
                            <span class="metric-value" id="overall-hit-rate">--.--%</span>
                        </div>
                        <div class="metric">
                            <span>平均响应时间</span>
                            <span class="metric-value" id="avg-response-time">-- ms</span>
                        </div>
                        <div class="metric">
                            <span>缓存大小</span>
                            <span class="metric-value" id="cache-size">-- MB</span>
                        </div>
                        <div class="metric">
                            <span>总调用次数</span>
                            <span class="metric-value" id="total-calls">0</span>
                        </div>
                    </div>

                    <div class="result-card" id="performance-metrics">
                        <h3>性能指标</h3>
                        <div class="metric">
                            <span>Gemini命中率</span>
                            <span class="metric-value" id="gemini-hit-rate">--.--%</span>
                        </div>
                        <div class="metric">
                            <span>地址翻译命中率</span>
                            <span class="metric-value" id="address-hit-rate">--.--%</span>
                        </div>
                        <div class="metric">
                            <span>渠道检测命中率</span>
                            <span class="metric-value" id="channel-hit-rate">--.--%</span>
                        </div>
                        <div class="metric">
                            <span>总节省时间</span>
                            <span class="metric-value" id="time-saved">-- ms</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 性能测试控制 -->
            <div class="test-section">
                <h2>🧪 性能测试</h2>
                <div class="test-controls">
                    <button class="btn-primary" onclick="runBasicPerformanceTest()">基础性能测试</button>
                    <button class="btn-primary" onclick="runGeminiCacheTest()">Gemini缓存测试</button>
                    <button class="btn-primary" onclick="runAddressCacheTest()">地址翻译缓存测试</button>
                    <button class="btn-primary" onclick="runChannelCacheTest()">渠道检测缓存测试</button>
                    <button class="btn-success" onclick="runStressTest()">压力测试</button>
                    <button class="btn-warning" onclick="clearAllCaches()">清空缓存</button>
                    <button class="btn-secondary" onclick="refreshStats()">刷新统计</button>
                    <button class="btn-secondary" onclick="exportReport()">导出报告</button>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" id="test-progress"></div>
                </div>

                <div class="test-log" id="test-log">
                    等待测试开始...
                </div>
            </div>

            <!-- 测试数据 -->
            <div class="test-section">
                <h2>📝 测试数据</h2>
                <div class="test-data">
                    <h3>测试用例数据</h3>
                    <textarea id="test-data-input" placeholder="在这里输入测试数据，每行一个测试用例...">订单编号：1234567890123456789
客户姓名：张先生
联系电话：+86-138-0000-1234
邮箱地址：<EMAIL>
接客地点：吉隆坡国际机场
目的地：双威酒店
乘客数量：2
行李数量：3

KL-ABC123456
Flight: MH370
Passenger: John Smith
Contact: +60-12-345-6789
Pickup: KLIA Terminal 1
Destination: Sunway Resort Hotel

订购人：李女士
订单编号：CD-DEF789012
航班信息：SQ123 抵达时间 14:30
接机地点：新加坡樟宜机场T3
送至：滨海湾金沙酒店
乘客：3人，行李：2件</textarea>
                </div>
            </div>

            <!-- 实时监控 -->
            <div class="test-section">
                <h2>📈 实时监控</h2>
                <div class="results-grid">
                    <div class="result-card">
                        <h3>实时性能图表</h3>
                        <canvas id="performance-chart" width="280" height="200" style="border: 1px solid #e9ecef; border-radius: 4px;"></canvas>
                    </div>
                    
                    <div class="result-card">
                        <h3>缓存健康状态</h3>
                        <div id="health-status">
                            <div class="metric">
                                <span>内存缓存</span>
                                <span class="metric-value">
                                    <span class="status-indicator status-error"></span>未知
                                </span>
                            </div>
                            <div class="metric">
                                <span>本地存储缓存</span>
                                <span class="metric-value">
                                    <span class="status-indicator status-error"></span>未知
                                </span>
                            </div>
                            <div class="metric">
                                <span>会话存储缓存</span>
                                <span class="metric-value">
                                    <span class="status-indicator status-error"></span>未知
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载缓存系统依赖 -->
    <script src="../../channel-detection-editor/module-container.js"></script>
    <script src="../../channel-detection-editor/cache-manager.js"></script>
    <script src="../../channel-detection-editor/cache-integration-adapter.js"></script>
    <script src="../../channel-detection-editor/cache-monitor-panel.js"></script>
    <script src="../../channel-detection-editor/error-handler.js"></script>
    <script src="../../channel-detection-editor/data.js"></script>
    <script src="../../hotels_by_region.js"></script>
    <script src="../../channel-detection-editor/airport-data.js"></script>
    <script src="../../channel-detection-editor/gemini-config.js"></script>
    <script src="../../channel-detection-editor/address-translator.js"></script>
    <script src="../../channel-detection-editor/channel-detector.js"></script>
    <script src="../../channel-detection-editor/field-mapper.js"></script>

    <script>
        // 全局测试状态
        let testState = {
            cacheManager: null,
            integrationAdapter: null,
            monitorPanel: null,
            fieldMapper: null,
            addressTranslator: null,
            channelDetector: null,
            isRunning: false,
            totalTests: 0,
            completedTests: 0
        };

        // 初始化测试环境
        document.addEventListener('DOMContentLoaded', async function() {
            logMessage('🚀 初始化缓存性能测试环境...');
            
            try {
                // 等待模块容器初始化
                if (window.moduleContainer) {
                    await window.moduleContainer.initialize();
                    
                    // 获取各个模块实例
                    testState.cacheManager = window.moduleContainer.get('cacheManager');
                    testState.integrationAdapter = window.moduleContainer.get('cacheIntegrationAdapter');
                    testState.monitorPanel = window.moduleContainer.get('cacheMonitorPanel');
                    testState.fieldMapper = window.moduleContainer.get('fieldMapper');
                    testState.addressTranslator = window.moduleContainer.get('addressTranslator');
                    testState.channelDetector = window.moduleContainer.get('channelDetector');
                    
                    logMessage('✅ 模块容器初始化完成');
                } else {
                    // 传统模式
                    testState.cacheManager = window.cacheManager;
                    testState.integrationAdapter = window.cacheIntegrationAdapter;
                    testState.monitorPanel = window.cacheMonitorPanel;
                    testState.fieldMapper = window.fieldMapper;
                    testState.addressTranslator = window.addressTranslator;
                    testState.channelDetector = window.channelDetector;
                    
                    logMessage('⚠️ 使用传统模式初始化');
                }
                
                // 为模块添加缓存增强
                if (testState.integrationAdapter) {
                    if (testState.fieldMapper) {
                        testState.integrationAdapter.enhanceFieldMapper(testState.fieldMapper);
                    }
                    if (testState.addressTranslator) {
                        testState.integrationAdapter.enhanceAddressTranslator(testState.addressTranslator);
                    }
                    if (testState.channelDetector) {
                        testState.integrationAdapter.enhanceChannelDetector(testState.channelDetector);
                    }
                }
                
                // 更新状态显示
                updateSystemStatus();
                
                // 启动定期更新
                setInterval(updateStats, 3000);
                
                logMessage('🎉 测试环境初始化完成');
                
            } catch (error) {
                logMessage(`❌ 初始化失败: ${error.message}`, 'error');
            }
        });

        // 更新系统状态
        function updateSystemStatus() {
            // 缓存管理器状态
            const managerStatus = document.getElementById('cache-manager-status');
            if (testState.cacheManager) {
                managerStatus.innerHTML = '<span class="status-indicator status-healthy"></span>已连接';
            }
            
            // 集成适配器状态
            const adapterStatus = document.getElementById('adapter-status');
            if (testState.integrationAdapter) {
                adapterStatus.innerHTML = '<span class="status-indicator status-healthy"></span>已连接';
            }
            
            // 监控面板状态
            const monitorStatus = document.getElementById('monitor-status');
            if (testState.monitorPanel) {
                monitorStatus.innerHTML = '<span class="status-indicator status-healthy"></span>已连接';
            }
        }

        // 更新统计信息
        async function updateStats() {
            if (!testState.cacheManager) return;
            
            try {
                const cacheStats = testState.cacheManager.getStats();
                const perfReport = testState.integrationAdapter ? 
                    testState.integrationAdapter.getPerformanceReport() : null;
                
                // 更新总体统计
                document.getElementById('overall-hit-rate').textContent = cacheStats.hitRate;
                document.getElementById('avg-response-time').textContent = cacheStats.avgResponseTimeMs + ' ms';
                document.getElementById('cache-size').textContent = cacheStats.bytesStoredMB + ' MB';
                document.getElementById('total-calls').textContent = cacheStats.hits + cacheStats.misses;
                
                // 更新模块性能指标
                if (perfReport) {
                    const geminiStats = perfReport.byModule.gemini || { hitRate: '0.00%' };
                    const addressStats = perfReport.byModule.address || { hitRate: '0.00%' };
                    const channelStats = perfReport.byModule.channel || { hitRate: '0.00%' };
                    
                    document.getElementById('gemini-hit-rate').textContent = geminiStats.hitRate;
                    document.getElementById('address-hit-rate').textContent = addressStats.hitRate;
                    document.getElementById('channel-hit-rate').textContent = channelStats.hitRate;
                    document.getElementById('time-saved').textContent = perfReport.overall.totalTimeSaved;
                }
                
                // 更新健康状态
                const healthStatus = await testState.cacheManager.getHealthStatus();
                updateHealthDisplay(healthStatus);
                
            } catch (error) {
                logMessage(`⚠️ 统计更新失败: ${error.message}`, 'error');
            }
        }

        // 更新健康状态显示
        function updateHealthDisplay(healthStatus) {
            const healthContainer = document.getElementById('health-status');
            if (!healthStatus || !healthStatus.backends) return;
            
            let html = '';
            for (const [backend, status] of Object.entries(healthStatus.backends)) {
                const statusClass = status.status === 'healthy' ? 'status-healthy' : 'status-error';
                const statusText = status.status === 'healthy' ? '健康' : '异常';
                
                html += `
                    <div class="metric">
                        <span>${backend}</span>
                        <span class="metric-value">
                            <span class="status-indicator ${statusClass}"></span>${statusText}
                        </span>
                    </div>
                `;
            }
            
            healthContainer.innerHTML = html;
        }

        // 基础性能测试
        async function runBasicPerformanceTest() {
            if (testState.isRunning) return;
            
            testState.isRunning = true;
            testState.totalTests = 20;
            testState.completedTests = 0;
            
            logMessage('🧪 开始基础性能测试...');
            updateProgress(0);
            
            const testData = getTestData().slice(0, 10);
            
            try {
                for (let i = 0; i < testData.length; i++) {
                    const data = testData[i];
                    
                    // 第一次调用（未缓存）
                    const start1 = Date.now();
                    await processTestData(data);
                    const time1 = Date.now() - start1;
                    
                    testState.completedTests++;
                    updateProgress((testState.completedTests / testState.totalTests) * 100);
                    
                    // 第二次调用（缓存命中）
                    const start2 = Date.now();
                    await processTestData(data);
                    const time2 = Date.now() - start2;
                    
                    testState.completedTests++;
                    updateProgress((testState.completedTests / testState.totalTests) * 100);
                    
                    logMessage(`测试 ${i + 1}: 首次 ${time1}ms, 缓存 ${time2}ms, 提升 ${((time1 - time2) / time1 * 100).toFixed(1)}%`);
                    
                    // 短暂延迟避免过于频繁的调用
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                logMessage('✅ 基础性能测试完成');
                
            } catch (error) {
                logMessage(`❌ 基础性能测试失败: ${error.message}`, 'error');
            } finally {
                testState.isRunning = false;
                updateProgress(100);
            }
        }

        // Gemini缓存测试
        async function runGeminiCacheTest() {
            if (testState.isRunning || !testState.fieldMapper) return;
            
            testState.isRunning = true;
            testState.totalTests = 10;
            testState.completedTests = 0;
            
            logMessage('🤖 开始Gemini缓存测试...');
            updateProgress(0);
            
            const testTexts = [
                '订单编号：1234567890123456789\n客户姓名：张先生\n联系电话：+86-138-0000-1234',
                'KL-ABC123456\nFlight: MH370\nPassenger: John Smith\nContact: +60-12-345-6789',
                '订购人：李女士\n订单编号：CD-DEF789012\n航班信息：SQ123'
            ];
            
            try {
                for (let i = 0; i < testTexts.length; i++) {
                    const text = testTexts[i];
                    
                    // 测试多次调用相同数据
                    for (let j = 0; j < 3; j++) {
                        const start = Date.now();
                        await testState.fieldMapper.processCompleteData(text);
                        const time = Date.now() - start;
                        
                        testState.completedTests++;
                        updateProgress((testState.completedTests / testState.totalTests) * 100);
                        
                        logMessage(`Gemini测试 ${i + 1}-${j + 1}: ${time}ms${j > 0 ? ' (缓存)' : ' (首次)'}`);
                        
                        await new Promise(resolve => setTimeout(resolve, 200));
                    }
                }
                
                logMessage('✅ Gemini缓存测试完成');
                
            } catch (error) {
                logMessage(`❌ Gemini缓存测试失败: ${error.message}`, 'error');
            } finally {
                testState.isRunning = false;
                updateProgress(100);
            }
        }

        // 地址翻译缓存测试
        async function runAddressCacheTest() {
            if (testState.isRunning || !testState.addressTranslator) return;
            
            testState.isRunning = true;
            testState.totalTests = 15;
            testState.completedTests = 0;
            
            logMessage('🌐 开始地址翻译缓存测试...');
            updateProgress(0);
            
            const addresses = [
                '吉隆坡国际机场',
                '双威酒店',
                '新加坡樟宜机场T3',
                '滨海湾金沙酒店',
                'KLIA Terminal 1'
            ];
            
            try {
                for (const address of addresses) {
                    // 每个地址测试3次
                    for (let i = 0; i < 3; i++) {
                        const start = Date.now();
                        await testState.addressTranslator.translateAddress(address);
                        const time = Date.now() - start;
                        
                        testState.completedTests++;
                        updateProgress((testState.completedTests / testState.totalTests) * 100);
                        
                        logMessage(`地址翻译 "${address}" 第${i + 1}次: ${time}ms`);
                        
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                }
                
                logMessage('✅ 地址翻译缓存测试完成');
                
            } catch (error) {
                logMessage(`❌ 地址翻译缓存测试失败: ${error.message}`, 'error');
            } finally {
                testState.isRunning = false;
                updateProgress(100);
            }
        }

        // 渠道检测缓存测试
        async function runChannelCacheTest() {
            if (testState.isRunning || !testState.channelDetector) return;
            
            testState.isRunning = true;
            testState.totalTests = 12;
            testState.completedTests = 0;
            
            logMessage('🔍 开始渠道检测缓存测试...');
            updateProgress(0);
            
            const testInputs = [
                '订单编号：1234567890123456789\n飞猪订单',
                'KL-ABC123456\nKlook 预订确认',
                'CD-DEF789012\n携程订单确认',
                'KK-GHI345678\nKKday 预订'
            ];
            
            try {
                for (const input of testInputs) {
                    // 每个输入测试3次
                    for (let i = 0; i < 3; i++) {
                        const start = Date.now();
                        testState.channelDetector.detectChannel(input);
                        const time = Date.now() - start;
                        
                        testState.completedTests++;
                        updateProgress((testState.completedTests / testState.totalTests) * 100);
                        
                        logMessage(`渠道检测 第${i + 1}次: ${time}ms`);
                        
                        await new Promise(resolve => setTimeout(resolve, 50));
                    }
                }
                
                logMessage('✅ 渠道检测缓存测试完成');
                
            } catch (error) {
                logMessage(`❌ 渠道检测缓存测试失败: ${error.message}`, 'error');
            } finally {
                testState.isRunning = false;
                updateProgress(100);
            }
        }

        // 压力测试
        async function runStressTest() {
            if (testState.isRunning) return;
            
            testState.isRunning = true;
            testState.totalTests = 100;
            testState.completedTests = 0;
            
            logMessage('💪 开始压力测试...');
            updateProgress(0);
            
            const testData = getTestData();
            const startTime = Date.now();
            
            try {
                const promises = [];
                
                for (let i = 0; i < testState.totalTests; i++) {
                    const data = testData[i % testData.length];
                    
                    const promise = processTestData(data).then(() => {
                        testState.completedTests++;
                        updateProgress((testState.completedTests / testState.totalTests) * 100);
                    });
                    
                    promises.push(promise);
                    
                    // 每10个请求添加小延迟
                    if (i % 10 === 0) {
                        await new Promise(resolve => setTimeout(resolve, 10));
                    }
                }
                
                await Promise.all(promises);
                
                const totalTime = Date.now() - startTime;
                const avgTime = totalTime / testState.totalTests;
                
                logMessage(`✅ 压力测试完成: ${testState.totalTests}次调用, 总时间${totalTime}ms, 平均${avgTime.toFixed(2)}ms/次`);
                
            } catch (error) {
                logMessage(`❌ 压力测试失败: ${error.message}`, 'error');
            } finally {
                testState.isRunning = false;
                updateProgress(100);
            }
        }

        // 处理测试数据
        async function processTestData(data) {
            const promises = [];
            
            // 字段映射处理
            if (testState.fieldMapper) {
                promises.push(testState.fieldMapper.processCompleteData(data));
            }
            
            // 渠道检测
            if (testState.channelDetector) {
                promises.push(Promise.resolve(testState.channelDetector.detectChannel(data)));
            }
            
            await Promise.all(promises);
        }

        // 获取测试数据
        function getTestData() {
            const textarea = document.getElementById('test-data-input');
            const lines = textarea.value.trim().split('\n').filter(line => line.trim());
            
            if (lines.length === 0) {
                return ['测试数据'];
            }
            
            // 按空行分割为不同的测试用例
            const testCases = [];
            let currentCase = [];
            
            for (const line of lines) {
                if (line.trim() === '') {
                    if (currentCase.length > 0) {
                        testCases.push(currentCase.join('\n'));
                        currentCase = [];
                    }
                } else {
                    currentCase.push(line);
                }
            }
            
            if (currentCase.length > 0) {
                testCases.push(currentCase.join('\n'));
            }
            
            return testCases;
        }

        // 清空所有缓存
        async function clearAllCaches() {
            if (!testState.cacheManager) return;
            
            try {
                await testState.cacheManager.clear();
                
                if (testState.integrationAdapter) {
                    testState.integrationAdapter.resetPerformanceStats();
                }
                
                logMessage('🧹 所有缓存已清空，性能统计已重置');
                updateStats();
                
            } catch (error) {
                logMessage(`❌ 清空缓存失败: ${error.message}`, 'error');
            }
        }

        // 刷新统计
        function refreshStats() {
            updateStats();
            logMessage('🔄 统计信息已刷新');
        }

        // 导出报告
        async function exportReport() {
            if (!testState.cacheManager) return;
            
            try {
                const report = {
                    timestamp: new Date().toISOString(),
                    cacheStats: testState.cacheManager.getStats(),
                    performanceReport: testState.integrationAdapter ? 
                        testState.integrationAdapter.getPerformanceReport() : null,
                    healthStatus: await testState.cacheManager.getHealthStatus(),
                    testEnvironment: {
                        userAgent: navigator.userAgent,
                        timestamp: new Date().toISOString(),
                        testType: 'performance-test'
                    }
                };
                
                const blob = new Blob([JSON.stringify(report, null, 2)], { 
                    type: 'application/json' 
                });
                
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `cache-performance-test-${Date.now()}.json`;
                a.click();
                
                URL.revokeObjectURL(url);
                
                logMessage('📄 性能测试报告已导出');
                
            } catch (error) {
                logMessage(`❌ 导出报告失败: ${error.message}`, 'error');
            }
        }

        // 更新进度条
        function updateProgress(percentage) {
            const progressFill = document.getElementById('test-progress');
            progressFill.style.width = `${percentage}%`;
        }

        // 添加日志消息
        function logMessage(message, level = 'info') {
            const log = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            
            const levelColors = {
                'info': '#e5e5e5',
                'success': '#4ade80',
                'warning': '#fbbf24',
                'error': '#ef4444'
            };
            
            const color = levelColors[level] || '#e5e5e5';
            
            log.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }
    </script>
</body>
</html>