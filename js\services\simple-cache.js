/**
 * ============================================================================
 * 简化缓存系统 (simple-cache.js) - 轻量级缓存解决方案
 * ============================================================================
 *
 * 【设计目标】
 * 替换过度复杂的缓存系统，提供简单、可靠的缓存功能。
 * 专注于核心需求：API调用缓存、地址翻译缓存、渠道检测缓存。
 *
 * 【核心特性】
 * - 内存缓存：基于Map的高速缓存
 * - TTL支持：自动过期清理
 * - 简单API：get/set/clear接口
 * - 统计信息：基本的命中率统计
 *
 * ============================================================================
 */

/**
 * 简化缓存类
 */
class SimpleCache {
    constructor(options = {}) {
        this.options = {
            maxSize: options.maxSize || 100,
            defaultTTL: options.defaultTTL || 10 * 60 * 1000, // 10分钟
            cleanupInterval: options.cleanupInterval || 5 * 60 * 1000, // 5分钟
            ...options
        };
        
        // 缓存存储
        this.cache = new Map();
        this.timers = new Map();
        
        // 统计信息
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0
        };
        
        // 启动清理定时器
        this.startCleanupTimer();
        
        console.log('✅ SimpleCache initialized');
    }

    /**
     * 获取缓存值
     * @param {string} key - 缓存键
     * @returns {any} 缓存值或null
     */
    get(key) {
        const entry = this.cache.get(key);
        
        if (!entry) {
            this.stats.misses++;
            return null;
        }
        
        // 检查是否过期
        if (Date.now() > entry.expireTime) {
            this.delete(key);
            this.stats.misses++;
            return null;
        }
        
        this.stats.hits++;
        return entry.value;
    }

    /**
     * 设置缓存值
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     * @param {number} ttl - 生存时间（毫秒）
     */
    set(key, value, ttl = null) {
        // 检查缓存大小限制
        if (this.cache.size >= this.options.maxSize && !this.cache.has(key)) {
            this._evictOldest();
        }
        
        const actualTTL = ttl || this.options.defaultTTL;
        const expireTime = Date.now() + actualTTL;
        
        // 清理旧的定时器
        if (this.timers.has(key)) {
            clearTimeout(this.timers.get(key));
        }
        
        // 设置缓存条目
        this.cache.set(key, {
            value,
            expireTime,
            createdAt: Date.now()
        });
        
        // 设置过期定时器
        const timer = setTimeout(() => {
            this.delete(key);
        }, actualTTL);
        
        this.timers.set(key, timer);
        this.stats.sets++;
    }

    /**
     * 删除缓存值
     * @param {string} key - 缓存键
     */
    delete(key) {
        if (this.cache.has(key)) {
            this.cache.delete(key);
            this.stats.deletes++;
        }
        
        if (this.timers.has(key)) {
            clearTimeout(this.timers.get(key));
            this.timers.delete(key);
        }
    }

    /**
     * 清空所有缓存
     */
    clear() {
        this.cache.clear();
        this.timers.forEach(timer => clearTimeout(timer));
        this.timers.clear();
        console.log('🧹 缓存已清空');
    }

    /**
     * 获取统计信息
     */
    getStats() {
        const total = this.stats.hits + this.stats.misses;
        const hitRate = total > 0 ? (this.stats.hits / total * 100).toFixed(2) : 0;
        
        return {
            ...this.stats,
            hitRate: `${hitRate}%`,
            size: this.cache.size,
            maxSize: this.options.maxSize
        };
    }

    /**
     * 创建缓存装饰器
     * @param {Function} fn - 原始函数
     * @param {Object} options - 选项
     */
    createCachedFunction(fn, options = {}) {
        const keyGenerator = options.keyGenerator || ((...args) => JSON.stringify(args));
        const ttl = options.ttl || this.options.defaultTTL;
        
        return async (...args) => {
            const key = keyGenerator(...args);
            
            // 尝试从缓存获取
            const cached = this.get(key);
            if (cached !== null) {
                return cached;
            }
            
            // 执行原始函数
            try {
                const result = await fn(...args);
                
                // 缓存结果
                if (result !== null && result !== undefined) {
                    this.set(key, result, ttl);
                }
                
                return result;
            } catch (error) {
                // 不缓存错误结果
                throw error;
            }
        };
    }

    /**
     * 淘汰最旧的缓存条目
     * @private
     */
    _evictOldest() {
        let oldestKey = null;
        let oldestTime = Date.now();
        
        for (const [key, entry] of this.cache.entries()) {
            if (entry.createdAt < oldestTime) {
                oldestTime = entry.createdAt;
                oldestKey = key;
            }
        }
        
        if (oldestKey) {
            this.delete(oldestKey);
        }
    }

    /**
     * 启动清理定时器
     * @private
     */
    startCleanupTimer() {
        setInterval(() => {
            this._cleanup();
        }, this.options.cleanupInterval);
    }

    /**
     * 清理过期条目
     * @private
     */
    _cleanup() {
        const now = Date.now();
        const expiredKeys = [];
        
        for (const [key, entry] of this.cache.entries()) {
            if (now > entry.expireTime) {
                expiredKeys.push(key);
            }
        }
        
        expiredKeys.forEach(key => this.delete(key));
        
        if (expiredKeys.length > 0) {
            console.log(`🧹 清理了 ${expiredKeys.length} 个过期缓存条目`);
        }
    }

    /**
     * 清理资源
     */
    dispose() {
        this.clear();
        console.log('🧹 SimpleCache disposed');
    }
}

/**
 * 全局缓存实例
 */
class GlobalCacheManager {
    constructor() {
        // 不同类型的缓存实例
        this.caches = {
            api: new SimpleCache({ maxSize: 50, defaultTTL: 10 * 60 * 1000 }), // API调用缓存
            address: new SimpleCache({ maxSize: 200, defaultTTL: 30 * 60 * 1000 }), // 地址翻译缓存
            channel: new SimpleCache({ maxSize: 100, defaultTTL: 5 * 60 * 1000 }) // 渠道检测缓存
        };
    }

    /**
     * 获取指定类型的缓存
     */
    getCache(type) {
        return this.caches[type] || this.caches.api;
    }

    /**
     * 获取所有缓存统计
     */
    getAllStats() {
        const stats = {};
        Object.entries(this.caches).forEach(([type, cache]) => {
            stats[type] = cache.getStats();
        });
        return stats;
    }

    /**
     * 清空所有缓存
     */
    clearAll() {
        Object.values(this.caches).forEach(cache => cache.clear());
    }

    /**
     * 清理资源
     */
    dispose() {
        Object.values(this.caches).forEach(cache => cache.dispose());
    }
}

/**
 * 创建简化缓存模块
 */
function createSimpleCacheModule() {
    return new GlobalCacheManager();
}

// 导出
if (typeof window !== 'undefined') {
    window.SimpleCache = SimpleCache;
    window.GlobalCacheManager = GlobalCacheManager;
    window.createSimpleCacheModule = createSimpleCacheModule;
}
