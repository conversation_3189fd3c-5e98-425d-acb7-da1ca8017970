/**
 * ============================================================================
 * 统一状态管理器 (state-manager.js) - 基于观察者模式的状态管理
 * ============================================================================
 *
 * 【核心功能】
 * 本文件实现了基于观察者模式的统一状态管理系统，用于替换项目中分散的状态管理机制。
 * 提供状态订阅、状态变更通知、状态持久化等功能。
 *
 * 【设计原则】
 * - 单一数据源：所有状态集中管理
 * - 不可变性：状态变更通过纯函数进行
 * - 可预测性：状态变更有明确的触发路径
 * - 可观察性：状态变更可被订阅和监听
 *
 * 【状态结构】
 * - app: 应用全局状态
 * - processing: 处理流程状态
 * - ui: 界面状态
 * - cache: 缓存状态
 * - errors: 错误状态
 *
 * ============================================================================
 */

/**
 * 状态管理器类
 */
class StateManager {
    constructor() {
        // 状态存储
        this.state = this._getInitialState();
        
        // 订阅者管理
        this.subscribers = new Map();
        this.middlewares = [];
        
        // 状态历史（用于调试和回滚）
        this.history = [];
        this.maxHistorySize = 50;
        
        // 持久化配置
        this.persistenceKey = 'app-state';
        this.persistentKeys = ['app.preferences', 'ui.theme'];
        
        console.log('✅ StateManager initialized');
    }

    /**
     * 获取初始状态
     * @private
     */
    _getInitialState() {
        return {
            app: {
                initialized: false,
                version: '1.0.0',
                preferences: {
                    autoDetection: true,
                    theme: 'light',
                    language: 'zh-CN'
                }
            },
            processing: {
                isProcessing: false,
                currentStep: null,
                progress: 0,
                lastProcessedContent: null,
                results: {
                    channel: null,
                    fields: null,
                    prompt: null,
                    fieldMapping: null
                }
            },
            ui: {
                activeModal: null,
                loading: false,
                notifications: [],
                theme: 'light'
            },
            cache: {
                enabled: true,
                stats: {
                    hits: 0,
                    misses: 0,
                    size: 0
                }
            },
            errors: {
                current: null,
                history: []
            }
        };
    }

    /**
     * 获取状态
     * @param {string} path - 状态路径，如 'app.preferences.theme'
     * @returns {any} 状态值
     */
    getState(path = null) {
        if (!path) return this.state;
        
        return this._getNestedValue(this.state, path);
    }

    /**
     * 设置状态
     * @param {string} path - 状态路径
     * @param {any} value - 新值
     * @param {Object} options - 选项
     */
    setState(path, value, options = {}) {
        const oldState = { ...this.state };
        const newState = this._setNestedValue({ ...this.state }, path, value);
        
        // 执行中间件
        const action = { type: 'SET_STATE', path, value, options };
        const processedState = this._applyMiddlewares(oldState, newState, action);
        
        // 更新状态
        this.state = processedState;
        
        // 记录历史
        this._addToHistory(oldState, action);
        
        // 通知订阅者
        this._notifySubscribers(path, value, oldState);
        
        // 持久化
        if (options.persist !== false) {
            this._persistState();
        }
        
        console.log(`📊 State updated: ${path}`, value);
    }

    /**
     * 订阅状态变更
     * @param {string} path - 状态路径
     * @param {Function} callback - 回调函数
     * @returns {Function} 取消订阅函数
     */
    subscribe(path, callback) {
        if (!this.subscribers.has(path)) {
            this.subscribers.set(path, new Set());
        }
        
        this.subscribers.get(path).add(callback);
        
        // 返回取消订阅函数
        return () => {
            const pathSubscribers = this.subscribers.get(path);
            if (pathSubscribers) {
                pathSubscribers.delete(callback);
                if (pathSubscribers.size === 0) {
                    this.subscribers.delete(path);
                }
            }
        };
    }

    /**
     * 批量更新状态
     * @param {Object} updates - 更新对象
     */
    batchUpdate(updates) {
        const oldState = { ...this.state };
        let newState = { ...this.state };
        
        // 应用所有更新
        Object.entries(updates).forEach(([path, value]) => {
            newState = this._setNestedValue(newState, path, value);
        });
        
        // 执行中间件
        const action = { type: 'BATCH_UPDATE', updates };
        const processedState = this._applyMiddlewares(oldState, newState, action);
        
        // 更新状态
        this.state = processedState;
        
        // 记录历史
        this._addToHistory(oldState, action);
        
        // 通知所有相关订阅者
        Object.keys(updates).forEach(path => {
            this._notifySubscribers(path, this._getNestedValue(this.state, path), oldState);
        });
        
        // 持久化
        this._persistState();
        
        console.log('📊 Batch state update completed', updates);
    }

    /**
     * 添加中间件
     * @param {Function} middleware - 中间件函数
     */
    addMiddleware(middleware) {
        this.middlewares.push(middleware);
    }

    /**
     * 获取嵌套值
     * @private
     */
    _getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }

    /**
     * 设置嵌套值
     * @private
     */
    _setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((current, key) => {
            if (!current[key] || typeof current[key] !== 'object') {
                current[key] = {};
            }
            return current[key];
        }, obj);
        
        target[lastKey] = value;
        return obj;
    }

    /**
     * 通知订阅者
     * @private
     */
    _notifySubscribers(path, newValue, oldState) {
        // 通知精确路径订阅者
        const exactSubscribers = this.subscribers.get(path);
        if (exactSubscribers) {
            exactSubscribers.forEach(callback => {
                try {
                    callback(newValue, this._getNestedValue(oldState, path));
                } catch (error) {
                    console.error('订阅者回调执行失败:', error);
                }
            });
        }
        
        // 通知父路径订阅者
        const pathParts = path.split('.');
        for (let i = pathParts.length - 1; i > 0; i--) {
            const parentPath = pathParts.slice(0, i).join('.');
            const parentSubscribers = this.subscribers.get(parentPath);
            if (parentSubscribers) {
                const parentNewValue = this._getNestedValue(this.state, parentPath);
                const parentOldValue = this._getNestedValue(oldState, parentPath);
                parentSubscribers.forEach(callback => {
                    try {
                        callback(parentNewValue, parentOldValue);
                    } catch (error) {
                        console.error('父路径订阅者回调执行失败:', error);
                    }
                });
            }
        }
    }

    /**
     * 应用中间件
     * @private
     */
    _applyMiddlewares(oldState, newState, action) {
        return this.middlewares.reduce((state, middleware) => {
            return middleware(oldState, state, action) || state;
        }, newState);
    }

    /**
     * 添加到历史记录
     * @private
     */
    _addToHistory(oldState, action) {
        this.history.push({
            timestamp: Date.now(),
            oldState: oldState,
            action: action
        });
        
        // 限制历史记录大小
        if (this.history.length > this.maxHistorySize) {
            this.history.shift();
        }
    }

    /**
     * 持久化状态
     * @private
     */
    _persistState() {
        try {
            const persistentState = {};
            this.persistentKeys.forEach(key => {
                const value = this._getNestedValue(this.state, key);
                if (value !== undefined) {
                    this._setNestedValue(persistentState, key, value);
                }
            });
            
            localStorage.setItem(this.persistenceKey, JSON.stringify(persistentState));
        } catch (error) {
            console.warn('状态持久化失败:', error);
        }
    }

    /**
     * 恢复持久化状态
     */
    restorePersistedState() {
        try {
            const persistedData = localStorage.getItem(this.persistenceKey);
            if (persistedData) {
                const persistedState = JSON.parse(persistedData);
                Object.keys(persistedState).forEach(key => {
                    const value = this._getNestedValue(persistedState, key);
                    if (value !== undefined) {
                        this.setState(key, value, { persist: false });
                    }
                });
                console.log('✅ 持久化状态已恢复');
            }
        } catch (error) {
            console.warn('恢复持久化状态失败:', error);
        }
    }

    /**
     * 清理资源
     */
    dispose() {
        this.subscribers.clear();
        this.middlewares = [];
        this.history = [];
        console.log('🧹 StateManager disposed');
    }
}

/**
 * 创建状态管理器模块
 */
function createStateManagerModule() {
    return new StateManager();
}

// 导出
if (typeof window !== 'undefined') {
    window.StateManager = StateManager;
    window.createStateManagerModule = createStateManagerModule;
}
