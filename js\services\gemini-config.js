/**
 * ============================================================================
 * Gemini AI配置管理模块 (gemini-config.js) - 防AI幻觉核心声明
 * ============================================================================
 *
 * 【防AI幻觉声明】
 * 本文件是AI服务配置和调用的统一管理中心，负责Gemini AI的完整生命周期。
 * 严禁AI基于文件名推测功能，必须完整阅读所有方法才能理解AI集成的完整流程。
 * 本文件不直接实现AI逻辑，仅提供配置管理和调用封装。
 *
 * 【重复开发防护】
 * 警告：本项目已存在完整的AI配置管理系统，严禁创建新的AI管理模块。
 * 任何新的AI服务集成必须在本文件中实现，而不是创建分散的AI调用代码。
 * 现有AI集成模式：[配置加载 → API密钥管理 → 调用封装 → 重试机制] - 严禁修改此约定。
 *
 * 【竞态问题防护】
 * AI服务调用竞态防护：
 * 1. API密钥访问序列化：异步加载和设置操作使用原子更新
 * 2. 重试机制保护：callGeminiAPIWithRetry方法防止并发重试冲突
 * 3. 本地存储同步：API密钥的存储和加载操作序列化执行
 * 4. 配置初始化锁：initialize方法确保配置完全就绪后再接受调用
 * 防护措施：使用async/await序列化、状态检查、超时控制确保AI调用的稳定性。
 *
 * 【声明与接口】
 * 导出：GeminiConfig类、createGeminiModule工厂函数
 * 导入：依赖localStorageManager模块容器服务
 * 主要接口：
 *   - constructor(localStorageManager)：依赖注入初始化
 *   - setApiKey(apiKey)：设置API密钥（异步持久化）
 *   - loadApiKeyFromStorage()：从本地存储加载密钥
 *   - validateApiKeyFormat(key)：验证API密钥格式
 *   - callGeminiAPIWithRetry(prompt, options)：带重试的API调用
 *
 * 【依赖关系网络】
 * 直接依赖：
 *   - localStorageManager：本地存储管理器 @REQUIRED
 *   - window对象：用于模块注册 @OPTIONAL
 *
 * 被依赖关系：
 *   - 被main.js注册为AI服务模块
 *   - 被fieldMapper依赖进行AI数据处理
 *   - 被所有需要AI功能的模块间接依赖
 *   - 影响整个应用的AI服务可用性
 *
 * 【加载时机与生命周期】
 * 加载时机：模块容器初始化阶段按依赖顺序加载
 * 初始化时机：模块容器调用工厂函数时异步初始化
 * 生命周期：
 *   1. 文件加载 → 2. 工厂函数注册 → 3. 依赖注入 → 4. 配置初始化 → 5. API密钥加载 → 6. 运行时AI调用 → 7. 配置更新 → 8. 实例销毁
 * 清理处理：API密钥自动持久化，无需特殊清理
 *
 * 【技术栈与环境要求】
 * 运行环境：浏览器环境（依赖fetch API和localStorage）
 * 技术栈：原生JavaScript ES6+，无框架依赖
 * 浏览器支持：现代浏览器（支持async/await、fetch API）
 * API要求：需要有效的Gemini AI API密钥
 *
 * 【核心功能说明】
 * 1. API密钥管理：安全存储和加载API密钥
 * 2. 配置验证：验证API密钥格式和有效性
 * 3. 调用封装：统一的AI API调用接口
 * 4. 重试机制：自动重试失败的API调用
 * 5. 持久化存储：API密钥的本地存储集成
 * 6. 模块集成：完整的模块容器集成支持
 *
 * 【相关文件索引】
 * - main.js：负责本模块的注册
 * - field-mapper.js：使用AI服务进行数据处理
 * - local-storage-manager.js：提供本地存储服务
 * - Gemini AI API：外部AI服务提供商
 *
 * 【修改记录】
 * v1.0.0 (2025-08-31) - 重构版本
 *   - 重构为ES6类语法
 *   - 添加完整的API密钥管理
 *   - 实现重试机制
 *   - 添加详细的防AI幻觉注释系统
 *   - 完善模块容器集成
 *
 * 【使用说明】
 * API密钥设置：通过setApiKey方法设置有效的Gemini API密钥
 * 自动加载：系统启动时自动从本地存储加载已保存的密钥
 * 调用方法：使用callGeminiAPIWithRetry进行可靠的AI调用
 * 错误处理：所有API调用都有完善的错误处理和重试机制
 *
 * ============================================================================
 */

/**
 * Gemini AI配置管理模块 - Refactored
 */
class GeminiConfig {
    constructor(localStorageManager) {
        this.localStorageManager = localStorageManager;
        this._initialized = false;
        this.config = {
            api: {
                apiKey: null,
                baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
                models: {
                    orderProcessing: 'gemini-2.5-flash',
                    promptOptimization: 'gemini-2.5-pro',
                    default: 'gemini-2.5-flash'
                }
            },
            features: {
                maxRetries: 3,
                retryDelay: 1000,
                timeout: 30000
            }
        };
        // ✅ 移除 this.initialize() 调用，由容器控制初始化时机
    }

    async initialize() {
        if (this._initialized) {
            console.log('⚠️ Gemini配置已初始化，跳过重复初始化');
            return;
        }
        
        console.log('✅ Gemini配置已初始化 (Refactored)');
        
        try {
            // 安全检查配置对象
            if (!this.config?.api) {
                throw new Error('Gemini配置对象未正确初始化');
            }
            
            // 优先从环境变量加载API密钥
            await this.loadApiKeyFromEnvironment();
            // 如果环境变量中没有，则从本地存储加载
            if (!this.config.api.apiKey) {
                await this.loadApiKeyFromStorage();
            }
            
            this._initialized = true;
            
        } catch (error) {
            console.error('❌ Gemini配置初始化失败:', error);
            this._initialized = true; // 标记为已初始化，避免重复尝试
            throw error;
        }
    }

    async loadApiKeyFromEnvironment() {
        try {
            // 检查全局环境变量 (通过HTML script标签注入)
            const envApiKey = window.GEMINI_API_KEY || 
                             window.process?.env?.GEMINI_API_KEY ||
                             this.getApiKeyFromUrlParams();

            if (envApiKey && this.validateApiKeyFormat(envApiKey)) {
                // 安全检查配置对象
                if (!this.config?.api) {
                    throw new Error('配置对象未正确初始化');
                }
                
                this.config.api.apiKey = envApiKey;
                console.log('🔑 从环境变量加载了Gemini API密钥');
                
                // 可选：将环境变量密钥保存到本地存储以便后续使用
                // (但不保存从URL参数获取的密钥)
                if (this.localStorageManager && !this.getApiKeyFromUrlParams()) {
                    await this.localStorageManager.saveData('gemini_api_key', envApiKey);
                }
                return true;
            }
            
            console.warn('⚠️ 未找到有效的API密钥环境变量');
            return false;
            
        } catch (error) {
            console.error('❌ 环境变量API密钥加载失败:', error);
            return false;
        }
    }

    getApiKeyFromUrlParams() {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('api_key');
        } catch (error) {
            return null;
        }
    }

    async loadApiKeyFromStorage() {
        if (this.localStorageManager) {
            const storedKey = await this.localStorageManager.loadData('gemini_api_key');
            if (storedKey && this.validateApiKeyFormat(storedKey)) {
                this.config.api.apiKey = storedKey;
                console.log('🔑 从本地存储加载了Gemini API密钥');
            }
        }
    }

    async setApiKey(apiKey) {
        if (this.validateApiKeyFormat(apiKey)) {
            this.config.api.apiKey = apiKey;
            if (this.localStorageManager) {
                await this.localStorageManager.saveData('gemini_api_key', apiKey);
            }
            console.log('✅ API密钥已设置并保存');
            return true;
        } else {
            console.error('❌ API密钥格式无效');
            return false;
        }
    }

    // 实现完整的Gemini API调用
    async callGeminiAPI(prompt, options = {}) {
        if (!this.isApiKeyValid()) {
            return {
                success: false,
                error: 'API密钥未设置或格式无效',
                data: null
            };
        }

        const useCase = options.useCase || 'default';
        const model = options.model || this.config.api.models[useCase] || this.config.api.models.default; // 支持按用例或选项动态选择模型
        const apiUrl = `${this.config.api.baseUrl}/models/${model}:generateContent?key=${this.config.api.apiKey}`;

        try {
            const requestBody = {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: options.temperature || 0.7,
                    topK: options.topK || 40,
                    topP: options.topP || 0.95,
                    maxOutputTokens: options.maxTokens || 2048,
                }
            };

            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                return {
                    success: true,
                    data: data.candidates[0].content.parts[0].text,
                    usage: data.usageMetadata
                };
            } else {
                return {
                    success: false,
                    error: 'API响应格式异常',
                    data: data
                };
            }

        } catch (error) {
            console.error('Gemini API调用失败:', error);
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }

    // 测试API连接的方法
    async testConnection(model = null) {
        const testPrompt = '请回复"Hello, World!"以测试API连接。';
        const options = model ? { model } : {};
        const result = await this.callGeminiAPI(testPrompt, options);

        if (result.success) {
            console.log('✅ Gemini API连接测试成功');
            console.log('📄 响应内容:', result.data);
            return { success: true, message: 'API连接正常' };
        } else {
            console.error('❌ Gemini API连接测试失败:', result.error);
            return { success: false, message: result.error };
        }
    }

    // 设置默认模型
    setDefaultModel(model) {
        this.config.api.models.default = model;
        console.log(`🔄 默认模型已设置为: ${model}`);
    }

    // 验证API密钥格式
    validateApiKeyFormat(key) {
        if (!key || typeof key !== 'string') {
            return false;
        }
        // Gemini API密钥通常以AIza开头，长度约39字符
        return key.startsWith('AIza') && key.length >= 35;
    }

    // 检查API密钥是否有效
    isApiKeyValid() {
        return this.config.api.apiKey && this.validateApiKeyFormat(this.config.api.apiKey);
    }

    // ... (other methods) ...
     async callGeminiAPIWithRetry(prompt, options = {}) {
        const maxRetries = this.config.features.maxRetries;
        const retryDelay = this.config.features.retryDelay;
        
        for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
            const result = await this.callGeminiAPI(prompt, options);
            
            if (result.success || attempt > maxRetries) {
                return result;
            }
            
            if (attempt <= maxRetries) {
                console.log(`⏳ 第${attempt}次重试，${retryDelay}ms后重试...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }
    }
}

// 模块工厂函数
function createGeminiModule(container) {
    const localStorageManager = container.get('localStorageManager');
    return new GeminiConfig(localStorageManager);
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('gemini', createGeminiModule, ['localStorageManager']);
    console.log('📦 GeminiConfig已注册到模块容器');
}