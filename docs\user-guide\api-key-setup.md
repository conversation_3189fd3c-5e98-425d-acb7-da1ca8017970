# API 密钥配置指南

## 🔐 安全设置 API 密钥

本项目支持多种方式安全地配置 Gemini API 密钥，避免在代码中硬编码敏感信息。

## 方法 1: 环境变量注入 (推荐)

### 1. 创建环境变量文件
```bash
# 复制示例文件
cp .env.example .env
```

### 2. 编辑 .env 文件
```bash
# 填入您的实际 API 密钥
GEMINI_API_KEY=您的实际API密钥
```

### 3. 在 HTML 中注入环境变量

如果您使用构建工具，可以在构建时替换 HTML 中的 `${GEMINI_API_KEY}` 占位符。

或者直接在 HTML 中设置：
```javascript
window.GEMINI_API_KEY = '您的实际API密钥';
```

## 方法 2: 运行时设置

### 通过浏览器控制台
```javascript
// 在浏览器开发者工具控制台中运行
window.GEMINI_API_KEY = '您的实际API密钥';
// 刷新页面以应用新设置
location.reload();
```

### 通过 URL 参数 (临时调试)
```
http://localhost:3000/?api_key=您的实际API密钥
```

## 方法 3: 编程设置

### 使用模块容器
```javascript
// 获取 Gemini 配置实例
const geminiConfig = container.get('gemini');

// 设置 API 密钥
await geminiConfig.setApiKey('您的实际API密钥');
```

## 🔍 验证设置

### 检查 API 密钥是否正确加载
打开浏览器开发者工具的控制台，您应该看到：
```
🔑 从环境变量加载了Gemini API密钥
```

### 测试 API 连接
```javascript
// 在控制台中测试
const gemini = container.get('gemini');
const result = await gemini.callGeminiAPIWithRetry('Hello, test message');
console.log(result);
```

## ⚠️ 安全注意事项

1. **不要提交真实 API 密钥** 到版本控制系统
2. **使用 .gitignore** 排除敏感文件：
   ```
   .env
   *.key
   secrets/
   ```
3. **定期轮换 API 密钥**
4. **限制 API 密钥权限** 仅为必要操作

## 📝 获取 API 密钥

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 创建新的 API 密钥
3. 复制密钥并安全存储
4. 在项目中配置密钥

## 🔧 故障排除

### API 密钥格式错误
确保密钥以 `AIza` 开头且长度超过30个字符。

### 密钥未加载
检查：
1. 环境变量是否正确设置
2. HTML 中的注入脚本是否执行
3. 浏览器控制台是否有错误信息

### API 调用失败
检查：
1. API 密钥是否有足够权限
2. 网络连接是否正常
3. API 配额是否充足
