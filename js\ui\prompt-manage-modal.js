/**
 * 【协同架构与依赖问题记录 - 2025-01-01】
 * 
 * 架构设计：
 * - 本文件实现Main-Modal深度协同的Modal端
 * - 与prompt-processor.js形成双向数据绑定
 * - 默认显示通用提示词，支持渠道切换
 * 
 * 依赖问题：
 * 1. 使用自注册模式（第1347行），但依赖channelDataManager等模块
 * 2. channelDataManager使用工厂模式注册，时机不同步
 * 3. 需要确保依赖模块在本模块初始化前完成注册
 * 4. 依赖声明已在第1359行添加，确保容器正确解析依赖关系
 * 
 * 渠道同步顺序问题及解决：
 * 1. 渠道规则编辑器 → 渠道列表数据
 *    - loadChannelsAndFragments()中先调用channelDataManager.initialize()
 *    - 再调用channelDataManager.loadChannels()确保数据最新
 * 2. 渠道列表数据 → 片段提示词显示
 *    - populateChannelSelector()按正确顺序填充渠道选择器
 *    - 通用提示词(generic)优先显示
 * 3. 用户在Modal中选择渠道 → 显示对应片段
 *    - switchToChannel()根据渠道加载对应片段提示词
 * 
 * 关键修复点：
 * - 第394行：确保channelDataManager先初始化再加载数据
 * - 第401行：通用提示词优先显示 ['generic', ...channels]
 * - 第485行：按渠道加载对应的片段提示词
 * 
 * ============================================================================
 * 
 * 提示词管理模态框 - Main-Modal深度协同版
 * 
 * 职责：
 * 1. 渠道提示词的可视化管理界面
 * 2. 通用提示词片段的展示和编辑（默认视图）
 * 3. 与PromptProcessor的深度协同和实时同步
 * 4. 提供提示词片段的CRUD操作
 * 5. 实时预览和字段映射显示
 * 
 * 协同特性：
 * - 接收来自main组件的订单处理结果
 * - 实时同步处理进度和字段映射
 * - 双向数据绑定和事件通信
 * - 片段编辑立即反映到main组件预览
 * 
 * 依赖：channelDataManager, promptComposer, localStorageManager
 */
class PromptManageModal {
    constructor(channelDataManager, promptComposer, localStorageManager, promptFragmentManager) {
        if (!channelDataManager || !promptComposer || !localStorageManager) {
            throw new Error("PromptManageModal requires all dependencies to be injected.");
        }
        
        this.channelDataManager = channelDataManager;
        this.promptComposer = promptComposer;
        this.localStorageManager = localStorageManager;
        this.promptFragmentManager = promptFragmentManager;
        
        // Modal状态
        this.isOpen = false;
        this.currentChannel = 'generic'; // 默认显示通用提示词
        this.currentFragments = [];
        this.selectedFragmentId = null;
        
        // Processor协同状态
        this.linkedProcessor = null;
        this.processingData = null;
        this.fieldMappingData = null;
        this.isRealTimeMode = true;
        
        // UI元素引用
        this.modalElement = null;
        this.channelListElement = null;
        this.fragmentsListElement = null;
        this.previewElement = null;
        this.editorElement = null;
        
        // 事件监听器
        this.eventListeners = new Map();
        
        console.log('✅ PromptManageModal initialized (协同版)');
    }

    /**
     * 关联PromptProcessor（建立深度协同）
     * @param {Object} processor - PromptProcessor实例
     */
    linkToProcessor(processor) {
        this.linkedProcessor = processor;
        console.log('🔗 PromptManageModal已关联到PromptProcessor');
        
        // 建立双向绑定
        if (processor && typeof processor.linkToModal === 'function') {
            processor.linkToModal(this);
        }
    }

    /**
     * 打开模态框
     * @param {Object} options - 打开选项
     */
    async openModal(options = {}) {
        try {
            console.log('📝 打开提示词管理模态框');
            
            // 如果已经打开，先关闭
            if (this.isOpen) {
                this.closeModal();
            }
            
            // 加载数据
            await this.loadChannelsAndFragments();
            
            // 创建模态框界面
            this.createModalUI();
            
            // 设置初始状态
            this.currentChannel = options.channel || 'generic';
            await this.switchToChannel(this.currentChannel);
            
            // 如果有关联的processor且有处理数据，显示映射
            if (this.linkedProcessor && this.linkedProcessor.lastProcessedFields) {
                this.displayFieldMapping();
            }
            
            this.isOpen = true;
            
            // 触发打开事件
            this.triggerEvent('modal_opened', {
                channel: this.currentChannel,
                fragments: this.currentFragments.length
            });
            
        } catch (error) {
            console.error('❌ 打开模态框失败:', error);
        }
    }

    /**
     * 关闭模态框
     */
    closeModal() {
        if (this.modalElement) {
            this.modalElement.remove();
            this.modalElement = null;
        }
        
        this.isOpen = false;
        this.selectedFragmentId = null;
        
        // 触发关闭事件
        this.triggerEvent('modal_closed', {
            lastChannel: this.currentChannel
        });
        
        console.log('✅ 提示词管理模态框已关闭');
    }

    /**
     * 创建模态框UI结构
     */
    createModalUI() {
        this.modalElement = document.createElement('div');
        this.modalElement.id = 'prompt-manage-modal';
        this.modalElement.className = 'prompt-editor-modal'; // 复用现有样式
        
        this.modalElement.innerHTML = this.getModalHTML();
        document.body.appendChild(this.modalElement);
        
        // 绑定事件
        this.bindModalEvents();
        
        // 初始化UI元素引用
        this.initializeUIElements();
        
        // 点击遮罩关闭
        this.modalElement.addEventListener('click', (e) => {
            if (e.target === this.modalElement) {
                this.closeModal();
            }
        });
    }

    /**
     * 获取模态框HTML结构
     * @returns {string} HTML字符串
     */
    getModalHTML() {
        return `
            <div class="prompt-editor-content">
                <div class="prompt-editor-header">
                    <h2>🎛️ 提示词片段管理（协同版）</h2>
                    <div class="modal-status">
                        <span id="current-channel-display">通用提示词</span>
                        <span id="processor-status">已关联</span>
                    </div>
                    <button class="prompt-editor-close" id="modal-close-btn">×</button>
                </div>
                
                <div class="prompt-editor-body">
                    <!-- 左侧：渠道列表和片段 -->
                    <div class="prompt-snippets-panel">
                        <div class="snippets-header">
                            <h3>🏷️ 渠道选择</h3>
                            <select id="channel-selector" class="channel-select">
                                <option value="generic">通用提示词 (默认)</option>
                            </select>
                        </div>
                        
                        <!-- 片段过滤和搜索 -->
                        <div class="snippets-filters-section">
                            <input type="text" id="fragments-search" class="snippets-search" placeholder="搜索片段...">
                            <div class="snippets-filters">
                                <button class="filter-tag active" data-filter="all">全部</button>
                                <button class="filter-tag" data-filter="base">基础模板</button>
                                <button class="filter-tag" data-filter="extraction">字段提取</button>
                                <button class="filter-tag" data-filter="validation">验证规则</button>
                                <button class="filter-tag" data-filter="output">输出格式</button>
                            </div>
                        </div>
                        
                        <!-- 片段列表 -->
                        <div class="snippets-list" id="fragments-list">
                            <div class="loading-placeholder">
                                <p>🔄 加载片段中...</p>
                            </div>
                        </div>
                        
                        <!-- 片段操作 -->
                        <div class="snippets-actions">
                            <button class="btn btn-sm btn-primary" id="add-fragment-btn">➕ 添加片段</button>
                            <button class="btn btn-sm btn-secondary" id="import-fragments-btn">📥 导入</button>
                        </div>
                    </div>
                    
                    <!-- 中间：片段编辑器 -->
                    <div class="prompt-editor-panel">
                        <div class="editor-header">
                            <h3 id="editor-title">📝 片段编辑</h3>
                            <div class="editor-actions">
                                <button class="btn btn-sm btn-success" id="save-fragment-btn">💾 保存</button>
                                <button class="btn btn-sm btn-danger" id="delete-fragment-btn">🗑️ 删除</button>
                            </div>
                        </div>
                        
                        <div class="editor-form" id="fragment-editor-form">
                            <div class="form-group">
                                <label class="form-label">片段名称</label>
                                <input type="text" id="fragment-name" class="form-input" placeholder="输入片段名称...">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">关联字段</label>
                                <select id="fragment-field" class="form-select">
                                    <option value="">选择关联字段...</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">片段类型</label>
                                <select id="fragment-category" class="form-select">
                                    <option value="base">基础模板</option>
                                    <option value="extraction">字段提取</option>
                                    <option value="validation">验证规则</option>
                                    <option value="output">输出格式</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">使用说明</label>
                                <input type="text" id="fragment-usage" class="form-input" placeholder="简要说明片段用途...">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">提示词模板</label>
                                <textarea id="fragment-template" class="form-textarea" placeholder="输入提示词模板内容...
支持占位符：
{input} - 输入内容
{field_name} - 特定字段名
{channel} - 当前渠道"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧：实时预览和映射显示 -->
                    <div class="ai-suggestions-panel">
                        <div class="ai-header">
                            <h3>👁️ 实时预览</h3>
                            <div class="preview-controls">
                                <button class="btn btn-sm btn-purple" id="refresh-preview-btn">🔄 刷新</button>
                                <button class="btn btn-sm btn-orange" id="test-fragment-btn">🧪 测试</button>
                            </div>
                        </div>
                        
                        <!-- 处理进度显示 -->
                        <div class="processing-status" id="processing-status">
                            <div class="status-item">
                                <span>关联处理器:</span>
                                <span id="processor-connection-status">已连接</span>
                            </div>
                        </div>
                        
                        <!-- 字段映射显示 -->
                        <div class="field-mapping-display" id="field-mapping-display">
                            <h4>🗺️ 字段映射</h4>
                            <div class="mapping-list" id="mapping-list">
                                <p class="placeholder">暂无字段映射数据</p>
                            </div>
                        </div>
                        
                        <!-- 预览区域 -->
                        <div class="ai-content" id="preview-content">
                            <h4>📋 片段预览</h4>
                            <div class="preview-area" id="preview-area">
                                <p class="placeholder">选择片段查看预览...</p>
                            </div>
                        </div>
                        
                        <!-- 协同状态显示 -->
                        <div class="sync-status" id="sync-status">
                            <h4>🔗 协同状态</h4>
                            <div class="sync-indicators">
                                <div class="sync-item">
                                    <span>实时模式:</span>
                                    <span id="realtime-status">启用</span>
                                </div>
                                <div class="sync-item">
                                    <span>最后同步:</span>
                                    <span id="last-sync-time">--</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 初始化UI元素引用
     */
    initializeUIElements() {
        this.channelListElement = document.getElementById('channel-selector');
        this.fragmentsListElement = document.getElementById('fragments-list');
        this.previewElement = document.getElementById('preview-area');
        this.editorElement = document.getElementById('fragment-editor-form');
    }

    /**
     * 绑定模态框事件
     */
    bindModalEvents() {
        // 关闭按钮
        document.getElementById('modal-close-btn').addEventListener('click', () => {
            this.closeModal();
        });
        
        // 渠道切换
        document.getElementById('channel-selector').addEventListener('change', (e) => {
            this.switchToChannel(e.target.value);
        });
        
        // 片段搜索（防抖）
        document.getElementById('fragments-search').addEventListener('input', (e) => {
            this.debounce(() => {
                this.searchFragments(e.target.value);
            }, 300)();
        });
        
        // 片段过滤
        document.querySelectorAll('.filter-tag').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.filterFragmentsByCategory(e.target.dataset.filter);
                // 更新过滤按钮状态
                document.querySelectorAll('.filter-tag').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
            });
        });
        
        // 片段操作按钮
        document.getElementById('add-fragment-btn').addEventListener('click', () => {
            this.createNewFragment();
        });
        
        document.getElementById('save-fragment-btn').addEventListener('click', () => {
            this.saveCurrentFragment();
        });
        
        document.getElementById('delete-fragment-btn').addEventListener('click', () => {
            this.deleteCurrentFragment();
        });
        
        // 预览控制
        document.getElementById('refresh-preview-btn').addEventListener('click', () => {
            this.refreshPreview();
        });
        
        document.getElementById('test-fragment-btn').addEventListener('click', () => {
            this.testCurrentFragment();
        });
        
        // 片段编辑实时预览
        document.getElementById('fragment-template').addEventListener('input', () => {
            this.debounce(() => {
                this.updatePreview();
            }, 500)();
        });
    }

    /**
     * 加载渠道和片段数据
     */
    async loadChannelsAndFragments() {
        try {
            console.log('🔄 加载渠道和片段数据...');
            
            // 1. 首先确保渠道数据管理器已初始化并同步了最新数据
            if (typeof this.channelDataManager.initialize === 'function') {
                await this.channelDataManager.initialize();
            }
            
            // 2. 重新加载渠道列表（确保从规则编辑器同步最新的渠道）
            await this.channelDataManager.loadChannels();
            
            // 3. 获取同步后的渠道列表
            const channels = this.channelDataManager.getAllChannels();
            console.log('📋 从渠道规则同步的渠道列表:', channels);
            
            // 4. 填充渠道选择器（通用提示词优先显示）
            this.populateChannelSelector(['generic', ...channels]);
            
            // 5. 加载当前渠道的片段提示词
            await this.loadFragmentsForChannel(this.currentChannel);
            
            console.log('✅ 渠道和片段数据加载完成 - 顺序: 渠道规则→渠道列表→片段提示词');
            
        } catch (error) {
            console.error('❌ 加载渠道和片段数据失败:', error);
        }
    }

    /**
     * 填充渠道选择器
     * @param {Array} channels - 渠道列表
     */
    populateChannelSelector(channels) {
        const selector = document.getElementById('channel-selector');
        selector.innerHTML = '';
        
        // 添加通用提示词选项（默认）
        const genericOption = document.createElement('option');
        genericOption.value = 'generic';
        genericOption.textContent = '🌐 通用提示词 (默认)';
        genericOption.selected = this.currentChannel === 'generic';
        selector.appendChild(genericOption);
        
        // 添加其他渠道选项
        channels.filter(channel => channel !== 'generic').forEach(channel => {
            const option = document.createElement('option');
            option.value = channel;
            option.textContent = `🏷️ ${channel}`;
            option.selected = this.currentChannel === channel;
            selector.appendChild(option);
        });
    }

    /**
     * 切换到指定渠道
     * 注意：渠道列表来自渠道规则编辑器，片段提示词按渠道显示
     * @param {string} channelName - 渠道名称
     */
    async switchToChannel(channelName) {
        try {
            console.log('🔀 切换到渠道:', channelName, '(从规则同步的渠道)');
            
            this.currentChannel = channelName;
            
            // 更新显示
            const displayElement = document.getElementById('current-channel-display');
            displayElement.textContent = channelName === 'generic' ? 
                '通用提示词 (默认)' : 
                `${channelName} 渠道片段`;
            
            // 加载渠道片段
            await this.loadFragmentsForChannel(channelName);
            
            // 通知关联的processor
            if (this.linkedProcessor) {
                this.sendToProcessor('channel_switched', {
                    channel: channelName,
                    fragments: this.currentFragments.length
                });
            }
            
            // 触发渠道切换事件
            this.triggerEvent('channel_switched', {
                channel: channelName,
                fragmentsCount: this.currentFragments.length
            });
            
        } catch (error) {
            console.error('❌ 切换渠道失败:', error);
        }
    }

    /**
     * 加载指定渠道的片段提示词
     * 渠道来源：从渠道规则编辑器同步的渠道列表
     * 片段来源：按渠道分类存储的提示词片段
     * @param {string} channelName - 渠道名称
     */
    async loadFragmentsForChannel(channelName) {
        try {
            console.log('📂 加载渠道片段提示词:', channelName, '(顺序: 先有渠道规则→再显示片段)');
            
            // 从本地存储加载片段提示词
            const allFragments = await this.localStorageManager.loadData('promptSnippets') || {};
            const channelFragments = allFragments[channelName] || [];
            
            this.currentFragments = Array.isArray(channelFragments) ? channelFragments : 
                (channelFragments.fragments || []);
            
            // 更新片段列表显示
            this.renderFragmentsList();
            
            console.log(`✅ 已加载 ${this.currentFragments.length} 个片段`);
            
        } catch (error) {
            console.error('❌ 加载渠道片段失败:', error);
            this.currentFragments = [];
            this.renderFragmentsList();
        }
    }

    /**
     * 渲染片段列表
     */
    renderFragmentsList() {
        if (!this.fragmentsListElement) return;
        
        this.fragmentsListElement.innerHTML = '';
        
        if (this.currentFragments.length === 0) {
            this.fragmentsListElement.innerHTML = `
                <div class="empty-state">
                    <p>🫗 当前渠道暂无片段</p>
                    <button class="btn btn-sm btn-primary" onclick="this.createNewFragment()">
                        ➕ 创建第一个片段
                    </button>
                </div>
            `;
            return;
        }
        
        this.currentFragments.forEach(fragment => {
            const fragmentElement = this.createFragmentElement(fragment);
            this.fragmentsListElement.appendChild(fragmentElement);
        });
    }

    /**
     * 创建片段元素
     * @param {Object} fragment - 片段数据
     * @returns {HTMLElement} 片段DOM元素
     */
    createFragmentElement(fragment) {
        const element = document.createElement('div');
        element.className = 'snippet-item';
        element.dataset.fragmentId = fragment.id;
        
        // 如果有字段映射，高亮显示
        const hasMapping = this.fieldMappingData && 
            this.fieldMappingData[fragment.field] && 
            this.fieldMappingData[fragment.field].hasMapping;
        
        if (hasMapping) {
            element.classList.add('has-field-mapping');
        }
        
        element.innerHTML = `
            <div class="snippet-name">${fragment.name || '未命名片段'}</div>
            <div class="snippet-meta">
                <span class="fragment-category">${this.getCategoryDisplay(fragment.category)}</span>
                <span class="fragment-field">${fragment.field || '无字段'}</span>
                ${hasMapping ? '<span class="mapping-indicator">🎯 已映射</span>' : ''}
            </div>
            <div class="snippet-preview">${(fragment.template || '').substring(0, 60)}...</div>
        `;
        
        // 点击选中片段
        element.addEventListener('click', () => {
            this.selectFragment(fragment.id);
        });
        
        return element;
    }

    /**
     * 获取分类显示名称
     * @param {string} category - 分类代码
     * @returns {string} 显示名称
     */
    getCategoryDisplay(category) {
        const categoryMap = {
            'base': '🏗️ 基础',
            'extraction': '⚡ 提取', 
            'validation': '✅ 验证',
            'output': '📤 输出'
        };
        return categoryMap[category] || '❓ 其他';
    }

    /**
     * 接收来自PromptProcessor的数据
     * @param {string} eventType - 事件类型
     * @param {Object} data - 事件数据
     */
    receiveFromProcessor(eventType, data) {
        console.log('📥 PromptManageModal收到Processor数据:', eventType, data);
        
        switch (eventType) {
            case 'channel_detected':
                this.handleChannelDetected(data);
                break;
            case 'fields_extracted':
                this.handleFieldsExtracted(data);
                break;
            case 'processing_completed':
                this.handleProcessingCompleted(data);
                break;
            case 'field_mapping_response':
                this.handleFieldMappingResponse(data);
                break;
            case 'preview_response':
                this.handlePreviewResponse(data);
                break;
            default:
                console.log('📋 其他Processor事件:', eventType, data);
        }
        
        // 更新最后同步时间
        this.updateSyncStatus();
    }

    /**
     * 处理渠道检测结果
     * @param {Object} data - 渠道检测数据
     */
    handleChannelDetected(data) {
        const { channel, step, totalSteps } = data;
        
        // 更新处理进度
        this.updateProcessingStatus(step, totalSteps, `渠道检测: ${channel.channel}`);
        
        // 如果检测到新渠道，提示切换
        if (channel.channel && channel.channel !== this.currentChannel) {
            this.showChannelSwitchSuggestion(channel.channel);
        }
    }

    /**
     * 处理字段提取结果
     * @param {Object} data - 字段提取数据
     */
    handleFieldsExtracted(data) {
        const { fields, fieldMapping, step, totalSteps } = data;
        
        // 更新处理进度
        this.updateProcessingStatus(step, totalSteps, '字段提取完成');
        
        // 保存字段映射数据
        this.fieldMappingData = fieldMapping;
        
        // 显示字段映射
        this.displayFieldMapping();
        
        // 高亮相关片段
        this.highlightMappedFragments();
    }

    /**
     * 处理完整处理结果
     * @param {Object} data - 处理结果数据
     */
    handleProcessingCompleted(data) {
        const { result, step, totalSteps } = data;
        
        // 更新处理进度
        this.updateProcessingStatus(totalSteps, totalSteps, '处理完成');
        
        // 保存完整处理数据
        this.processingData = result;
        
        // 更新预览
        this.updatePreview();
    }

    /**
     * 显示字段映射
     */
    displayFieldMapping() {
        const mappingElement = document.getElementById('mapping-list');
        if (!mappingElement || !this.fieldMappingData) return;
        
        mappingElement.innerHTML = '';
        
        const mappingEntries = Object.entries(this.fieldMappingData);
        if (mappingEntries.length === 0) {
            mappingElement.innerHTML = '<p class="placeholder">暂无字段映射</p>';
            return;
        }
        
        mappingEntries.forEach(([fieldName, mappingInfo]) => {
            const mappingItem = document.createElement('div');
            mappingItem.className = 'mapping-item';
            
            mappingItem.innerHTML = `
                <div class="field-info">
                    <strong>${fieldName}</strong>
                    <span class="field-value">${mappingInfo.value || '无值'}</span>
                </div>
                <div class="fragments-info">
                    ${mappingInfo.fragments.length > 0 ? 
                        `🎯 ${mappingInfo.fragments.length} 个片段` : 
                        '❌ 无匹配片段'
                    }
                </div>
            `;
            
            // 点击查看相关片段
            if (mappingInfo.fragments.length > 0) {
                mappingItem.style.cursor = 'pointer';
                mappingItem.addEventListener('click', () => {
                    this.showRelatedFragments(fieldName, mappingInfo.fragments);
                });
            }
            
            mappingElement.appendChild(mappingItem);
        });
    }

    /**
     * 高亮映射的片段
     */
    highlightMappedFragments() {
        if (!this.fieldMappingData) return;
        
        // 移除所有高亮
        document.querySelectorAll('.snippet-item').forEach(item => {
            item.classList.remove('has-field-mapping');
        });
        
        // 添加高亮到有映射的片段
        Object.values(this.fieldMappingData).forEach(mappingInfo => {
            if (mappingInfo.hasMapping && mappingInfo.fragments.length > 0) {
                mappingInfo.fragments.forEach(fragment => {
                    const fragmentElement = document.querySelector(
                        `[data-fragment-id="${fragment.id}"]`
                    );
                    if (fragmentElement) {
                        fragmentElement.classList.add('has-field-mapping');
                    }
                });
            }
        });
    }

    /**
     * 发送数据到PromptProcessor
     * @param {string} eventType - 事件类型
     * @param {Object} data - 数据
     */
    sendToProcessor(eventType, data) {
        if (!this.linkedProcessor) {
            console.warn('⚠️ 未关联PromptProcessor，无法发送数据');
            return;
        }
        
        try {
            if (typeof this.linkedProcessor.receiveFromModal === 'function') {
                this.linkedProcessor.receiveFromModal(eventType, data);
            }
            
        } catch (error) {
            console.error('❌ 发送数据到Processor失败:', error);
        }
    }

    /**
     * 更新同步状态显示
     */
    updateSyncStatus() {
        const timeElement = document.getElementById('last-sync-time');
        if (timeElement) {
            timeElement.textContent = new Date().toLocaleTimeString();
        }
    }

    /**
     * 更新处理状态显示
     * @param {number} current - 当前步骤
     * @param {number} total - 总步骤
     * @param {string} message - 状态消息
     */
    updateProcessingStatus(current, total, message) {
        const statusElement = document.getElementById('processing-status');
        if (!statusElement) return;
        
        const progressHtml = `
            <div class="status-item">
                <span>处理进度:</span>
                <span>${current}/${total} - ${message}</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${(current/total)*100}%"></div>
            </div>
        `;
        
        statusElement.innerHTML = progressHtml;
    }

    /**
     * 防抖函数
     * @param {Function} func - 要执行的函数
     * @param {number} wait - 延迟时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 选择片段
     * @param {string} fragmentId - 片段ID
     */
    selectFragment(fragmentId) {
        const fragment = this.currentFragments.find(f => f.id === fragmentId);
        if (!fragment) return;
        
        // 更新选中状态
        this.selectedFragmentId = fragmentId;
        
        // 更新UI选中状态
        document.querySelectorAll('.snippet-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-fragment-id="${fragmentId}"]`)?.classList.add('active');
        
        // 填充编辑器
        this.populateEditor(fragment);
        
        // 更新预览
        this.updatePreview();
        
        console.log('📌 已选中片段:', fragment.name);
    }

    /**
     * 填充编辑器
     * @param {Object} fragment - 片段数据
     */
    populateEditor(fragment) {
        document.getElementById('fragment-name').value = fragment.name || '';
        document.getElementById('fragment-field').value = fragment.field || '';
        document.getElementById('fragment-category').value = fragment.category || 'extraction';
        document.getElementById('fragment-usage').value = fragment.usage || '';
        document.getElementById('fragment-template').value = fragment.template || '';
        
        // 更新编辑器标题
        document.getElementById('editor-title').textContent = `📝 编辑: ${fragment.name || '未命名片段'}`;
    }

    /**
     * 创建新片段
     */
    createNewFragment() {
        const newFragment = {
            id: `fragment_${Date.now()}`,
            name: '新片段',
            field: '',
            category: 'extraction',
            usage: '',
            template: '',
            channel: this.currentChannel,
            createdAt: new Date().toISOString()
        };
        
        // 添加到当前片段列表
        this.currentFragments.push(newFragment);
        
        // 重新渲染列表
        this.renderFragmentsList();
        
        // 选中新片段
        this.selectFragment(newFragment.id);
        
        // 聚焦到名称输入框
        setTimeout(() => {
            document.getElementById('fragment-name').focus();
            document.getElementById('fragment-name').select();
        }, 100);
        
        console.log('➕ 创建新片段:', newFragment.id);
    }

    /**
     * 保存当前片段
     */
    async saveCurrentFragment() {
        if (!this.selectedFragmentId) {
            alert('请先选择要保存的片段');
            return;
        }
        
        const fragment = this.currentFragments.find(f => f.id === this.selectedFragmentId);
        if (!fragment) return;
        
        // 从编辑器获取数据
        fragment.name = document.getElementById('fragment-name').value.trim();
        fragment.field = document.getElementById('fragment-field').value.trim();
        fragment.category = document.getElementById('fragment-category').value;
        fragment.usage = document.getElementById('fragment-usage').value.trim();
        fragment.template = document.getElementById('fragment-template').value.trim();
        fragment.updatedAt = new Date().toISOString();
        
        if (!fragment.name) {
            alert('请输入片段名称');
            return;
        }
        
        if (!fragment.template) {
            alert('请输入片段模板');
            return;
        }
        
        try {
            // 保存到本地存储
            await this.saveFragmentsToStorage();
            
            // 重新渲染列表
            this.renderFragmentsList();
            
            // 重新选中当前片段
            this.selectFragment(fragment.id);
            
            // 通知processor片段已更新
            if (this.linkedProcessor) {
                this.sendToProcessor('prompt_fragments_updated', {
                    fragments: this.currentFragments,
                    updatedFragment: fragment,
                    channel: this.currentChannel
                });
            }
            
            // 显示成功消息
            this.showMessage('💾 片段保存成功', 'success');
            
            console.log('💾 片段保存成功:', fragment.name);
            
        } catch (error) {
            console.error('❌ 保存片段失败:', error);
            this.showMessage('❌ 保存片段失败', 'error');
        }
    }

    /**
     * 删除当前片段
     */
    async deleteCurrentFragment() {
        if (!this.selectedFragmentId) {
            alert('请先选择要删除的片段');
            return;
        }
        
        const fragment = this.currentFragments.find(f => f.id === this.selectedFragmentId);
        if (!fragment) return;
        
        if (!confirm(`确定要删除片段"${fragment.name}"吗？`)) {
            return;
        }
        
        try {
            // 从列表中移除
            const index = this.currentFragments.findIndex(f => f.id === this.selectedFragmentId);
            if (index > -1) {
                this.currentFragments.splice(index, 1);
            }
            
            // 保存到本地存储
            await this.saveFragmentsToStorage();
            
            // 清空编辑器
            this.clearEditor();
            this.selectedFragmentId = null;
            
            // 重新渲染列表
            this.renderFragmentsList();
            
            // 通知processor片段已更新
            if (this.linkedProcessor) {
                this.sendToProcessor('prompt_fragments_updated', {
                    fragments: this.currentFragments,
                    deletedFragment: fragment,
                    channel: this.currentChannel
                });
            }
            
            // 显示成功消息
            this.showMessage('🗑️ 片段删除成功', 'success');
            
            console.log('🗑️ 片段删除成功:', fragment.name);
            
        } catch (error) {
            console.error('❌ 删除片段失败:', error);
            this.showMessage('❌ 删除片段失败', 'error');
        }
    }

    /**
     * 保存片段到本地存储
     */
    async saveFragmentsToStorage() {
        const allFragments = await this.localStorageManager.loadData('promptSnippets') || {};
        allFragments[this.currentChannel] = this.currentFragments;
        await this.localStorageManager.saveData('promptSnippets', allFragments);
    }

    /**
     * 清空编辑器
     */
    clearEditor() {
        document.getElementById('fragment-name').value = '';
        document.getElementById('fragment-field').value = '';
        document.getElementById('fragment-category').value = 'extraction';
        document.getElementById('fragment-usage').value = '';
        document.getElementById('fragment-template').value = '';
        document.getElementById('editor-title').textContent = '📝 片段编辑';
    }

    /**
     * 更新预览
     */
    updatePreview() {
        const previewArea = document.getElementById('preview-area');
        if (!previewArea) return;
        
        if (!this.selectedFragmentId) {
            previewArea.innerHTML = '<p class="placeholder">选择片段查看预览...</p>';
            return;
        }
        
        const fragment = this.currentFragments.find(f => f.id === this.selectedFragmentId);
        if (!fragment) return;
        
        const template = document.getElementById('fragment-template').value;
        if (!template) {
            previewArea.innerHTML = '<p class="placeholder">输入模板内容查看预览...</p>';
            return;
        }
        
        // 生成预览内容
        let previewContent = template;
        
        // 替换常见占位符
        const placeholders = {
            '{input}': this.processingData?.originalContent || '【订单内容】',
            '{channel}': this.currentChannel,
            '{field_name}': fragment.field || '【字段名】'
        };
        
        Object.entries(placeholders).forEach(([placeholder, value]) => {
            previewContent = previewContent.replace(new RegExp(placeholder, 'g'), value);
        });
        
        previewArea.innerHTML = `
            <div class="preview-header">
                <strong>${fragment.name || '未命名片段'}</strong>
                <span class="preview-meta">${this.getCategoryDisplay(fragment.category)}</span>
            </div>
            <div class="preview-content">
                <pre>${previewContent}</pre>
            </div>
            <div class="preview-footer">
                <small>长度: ${previewContent.length} 字符</small>
            </div>
        `;
    }

    /**
     * 刷新预览
     */
    refreshPreview() {
        this.updatePreview();
        
        // 如果有关联的processor，请求最新预览
        if (this.linkedProcessor && this.selectedFragmentId) {
            const fragment = this.currentFragments.find(f => f.id === this.selectedFragmentId);
            if (fragment) {
                this.sendToProcessor('preview_request', {
                    fragment: fragment,
                    requestId: Date.now()
                });
            }
        }
    }

    /**
     * 测试当前片段
     */
    async testCurrentFragment() {
        if (!this.selectedFragmentId) {
            alert('请先选择要测试的片段');
            return;
        }
        
        const fragment = this.currentFragments.find(f => f.id === this.selectedFragmentId);
        if (!fragment) return;
        
        const template = document.getElementById('fragment-template').value;
        if (!template) {
            alert('请输入片段模板');
            return;
        }
        
        // 创建测试数据
        const testData = {
            input: this.processingData?.originalContent || '测试订单内容',
            channel: this.currentChannel,
            field: fragment.field
        };
        
        // 显示测试结果
        this.showTestResult(fragment, template, testData);
    }

    /**
     * 显示测试结果
     * @param {Object} fragment - 片段数据
     * @param {string} template - 模板内容
     * @param {Object} testData - 测试数据
     */
    showTestResult(fragment, template, testData) {
        const testModal = document.createElement('div');
        testModal.className = 'test-modal-overlay';
        testModal.innerHTML = `
            <div class="test-modal">
                <div class="test-modal-header">
                    <h3>🧪 片段测试: ${fragment.name}</h3>
                    <button class="close-test-modal">×</button>
                </div>
                <div class="test-modal-body">
                    <div class="test-section">
                        <h4>📝 原始模板</h4>
                        <pre class="test-content">${template}</pre>
                    </div>
                    <div class="test-section">
                        <h4>🔧 处理结果</h4>
                        <pre class="test-content">${this.processTemplate(template, testData)}</pre>
                    </div>
                    <div class="test-section">
                        <h4>📊 测试数据</h4>
                        <pre class="test-content">${JSON.stringify(testData, null, 2)}</pre>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(testModal);
        
        // 绑定关闭事件
        testModal.querySelector('.close-test-modal').addEventListener('click', () => {
            testModal.remove();
        });
        
        testModal.addEventListener('click', (e) => {
            if (e.target === testModal) {
                testModal.remove();
            }
        });
    }

    /**
     * 处理模板（替换占位符）
     * @param {string} template - 模板字符串
     * @param {Object} data - 数据对象
     * @returns {string} 处理后的字符串
     */
    processTemplate(template, data) {
        let processed = template;
        
        // 替换占位符
        Object.entries(data).forEach(([key, value]) => {
            const placeholder = `{${key}}`;
            processed = processed.replace(new RegExp(placeholder, 'g'), value);
        });
        
        return processed;
    }

    /**
     * 搜索片段
     * @param {string} searchTerm - 搜索词
     */
    searchFragments(searchTerm) {
        const items = document.querySelectorAll('.snippet-item');
        const term = searchTerm.toLowerCase();
        
        items.forEach(item => {
            const name = item.querySelector('.snippet-name').textContent.toLowerCase();
            const field = item.querySelector('.fragment-field').textContent.toLowerCase();
            const preview = item.querySelector('.snippet-preview').textContent.toLowerCase();
            
            const matches = name.includes(term) || field.includes(term) || preview.includes(term);
            item.style.display = matches ? 'block' : 'none';
        });
    }

    /**
     * 按分类过滤片段
     * @param {string} category - 分类
     */
    filterFragmentsByCategory(category) {
        const items = document.querySelectorAll('.snippet-item');
        
        items.forEach(item => {
            const fragmentId = item.dataset.fragmentId;
            const fragment = this.currentFragments.find(f => f.id === fragmentId);
            
            if (category === 'all' || (fragment && fragment.category === category)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    /**
     * 显示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    showMessage(message, type = 'info') {
        const messageElement = document.createElement('div');
        messageElement.className = `message-toast ${type}`;
        messageElement.textContent = message;
        messageElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
            color: white;
            border-radius: 4px;
            z-index: 10000;
            animation: slideIn 0.3s ease;
        `;
        
        document.body.appendChild(messageElement);
        
        // 3秒后自动移除
        setTimeout(() => {
            messageElement.remove();
        }, 3000);
    }

    /**
     * 显示渠道切换建议
     * @param {string} suggestedChannel - 建议的渠道
     */
    showChannelSwitchSuggestion(suggestedChannel) {
        const suggestionElement = document.getElementById('current-channel-display');
        if (!suggestionElement) return;
        
        suggestionElement.innerHTML = `
            ${this.currentChannel === 'generic' ? '通用提示词' : this.currentChannel}
            <button class="btn btn-sm btn-orange switch-suggestion" 
                    onclick="this.switchToChannel('${suggestedChannel}')">
                切换到 ${suggestedChannel}
            </button>
        `;
        
        // 3秒后恢复正常显示
        setTimeout(() => {
            suggestionElement.textContent = this.currentChannel === 'generic' ? '通用提示词' : this.currentChannel;
        }, 5000);
    }

    /**
     * 显示相关片段
     * @param {string} fieldName - 字段名
     * @param {Array} fragments - 相关片段列表
     */
    showRelatedFragments(fieldName, fragments) {
        if (fragments.length === 0) return;
        
        // 如果只有一个片段，直接选中
        if (fragments.length === 1) {
            this.selectFragment(fragments[0].id);
            return;
        }
        
        // 多个片段时显示选择对话框
        const selectionHtml = fragments.map(fragment => `
            <div class="related-fragment-item" data-fragment-id="${fragment.id}">
                <strong>${fragment.name}</strong>
                <p>${fragment.usage || '无描述'}</p>
            </div>
        `).join('');
        
        const selectionModal = document.createElement('div');
        selectionModal.className = 'selection-modal-overlay';
        selectionModal.innerHTML = `
            <div class="selection-modal">
                <div class="selection-header">
                    <h3>选择相关片段: ${fieldName}</h3>
                    <button class="close-selection">×</button>
                </div>
                <div class="selection-body">
                    ${selectionHtml}
                </div>
            </div>
        `;
        
        document.body.appendChild(selectionModal);
        
        // 绑定选择事件
        selectionModal.querySelectorAll('.related-fragment-item').forEach(item => {
            item.addEventListener('click', () => {
                const fragmentId = item.dataset.fragmentId;
                this.selectFragment(fragmentId);
                selectionModal.remove();
            });
        });
        
        // 绑定关闭事件
        selectionModal.querySelector('.close-selection').addEventListener('click', () => {
            selectionModal.remove();
        });
        
        selectionModal.addEventListener('click', (e) => {
            if (e.target === selectionModal) {
                selectionModal.remove();
            }
        });
    }

    /**
     * 触发事件
     * @param {string} eventType - 事件类型
     * @param {Object} data - 事件数据
     */
    triggerEvent(eventType, data) {
        const event = new CustomEvent('prompt-modal-event', {
            detail: {
                type: eventType,
                data: data,
                timestamp: new Date().toISOString(),
                modal: this
            }
        });
        
        window.dispatchEvent(event);
    }
}

// ============================================================================
// 自注册代码已移除 - 阶段3统一架构完成
// ============================================================================
// 
// 历史: 原有自注册代码已在阶段3中移除
// 原因: 统一为main.js集中注册模式，避免注册时机冲突
// 替代: 使用window.createPromptManageModalModule工厂函数
// 注册位置: js/core/main.js 第156-157行

// ============================================================================
// 标准工厂函数 - 阶段1统一工厂模式
// ============================================================================

/**
 * 标准工厂函数：创建PromptManageModal实例
 * 用于main.js中的集中注册，与自注册形成双轨制兼容
 * @param {ModuleContainer} container - 模块容器实例
 * @returns {PromptManageModal} PromptManageModal实例
 */
window.createPromptManageModalModule = function(container) {
    const channelDataManager = container.get('channelDataManager');
    const promptComposer = container.get('promptComposer');
    const localStorageManager = container.get('localStorageManager');
    const promptFragmentManager = container.get('promptFragmentManager');
    
    return new PromptManageModal(
        channelDataManager, 
        promptComposer, 
        localStorageManager, 
        promptFragmentManager
    );
};