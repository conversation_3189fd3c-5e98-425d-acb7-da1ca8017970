# 系统架构和关键技术决策

## 架构概述

**纯前端模块化架构** - 基于依赖注入容器的现代化设计

项目采用**零第三方依赖**的原生JavaScript架构，通过自研的依赖注入容器实现模块化管理，彻底解决了传统全局变量污染问题。

## 核心架构模式

### 1. 依赖注入容器模式

**核心文件**: `module-container.js`

```javascript
// 统一的模块注册和依赖管理
window.registerModule('serviceName', factory, dependencies);
const service = window.container.get('serviceName');
```

**优势**:
- 消除全局变量污染（从12个全局实例优化为1个容器）
- 明确的依赖关系图
- 支持模块热插拔和懒加载
- 便于单元测试和模拟

### 2. 分层架构模式

```
展示层 (UI Layer)
├── index.html - 主界面
├── rule-editor.js - 规则编辑器
└── prompt-editor.js - 提示词编辑器

业务层 (Business Layer)
├── app.js - 主应用程序逻辑
├── channel-detector.js - 渠道检测器
├── field-mapper.js - 字段映射器
└── prompt-composer.js - 提示词组合器

服务层 (Service Layer)
├── cache-manager.js - 缓存管理
├── address-translator.js - 地址翻译
└── gemini-config.js - AI服务配置

工具层 (Utility Layer)
├── crypto-utils.js - 加密工具
├── local-storage-manager.js - 存储管理
└── error-handler.js - 错误处理

数据层 (Data Layer)
├── config.js - 配置管理
├── data.js - 静态数据
└── hotels_by_region.js - 酒店数据
```

### 3. 缓存系统架构

**三层缓存策略**:

```javascript
// 内存缓存 (L1) - 最快访问
Map cache - 毫秒级响应

// 会话缓存 (L2) - 标签页生命周期
sessionStorage - 页面刷新保持

// 持久缓存 (L3) - 跨会话保持
localStorage - 长期缓存
```

**缓存优化效果**:
- Gemini API调用: 15-25秒 → 50-200ms (**+98%**)
- 地址翻译处理: 100-500ms → 5-20ms (**+96%**)
- 渠道检测速度: 20-50ms → 1-5ms (**+90%**)

### 4. 事件驱动架构

**发布-订阅模式**: 实现模块间松耦合通信

```javascript
// 渠道数据更新事件
channelDataManager.publishChannelsUpdate();
promptEditor.handleChannelsUpdated();
ruleEditor.handleChannelsUpdated();
```

## 关键技术决策

### 1. 零依赖策略

**决策**: 100%原生JavaScript实现
**理由**:
- 避免供应链安全风险
- 消除版本兼容性问题
- 简化部署和维护
- 提高项目可控性

**实现方式**:
- 自研依赖注入容器
- 原生实现路由和状态管理
- 手工实现常用工具函数
- 纯CSS实现所有样式效果

### 2. 文件加载顺序策略

**严格的依赖顺序**:
```html
<!-- 1. 容器系统 (最优先) -->
<script src="module-container.js"></script>

<!-- 2. 核心服务 -->
<script src="error-handler.js"></script>
<script src="local-storage-manager.js"></script>
<script src="crypto-utils.js"></script>

<!-- 3. 数据层 -->
<script src="../hotels_by_region.js"></script>
<script src="airport-data.js"></script>
<script src="config.js"></script>

<!-- 4. 业务逻辑 -->
<script src="gemini-config.js"></script>
<script src="channel-detector.js"></script>
<script src="field-mapper.js"></script>

<!-- 5. 缓存系统 -->
<script src="cache-manager.js"></script>
<script src="cache-integration-adapter.js"></script>

<!-- 6. 主应用 (最后) -->
<script src="app.js"></script>
<script src="main.js"></script>
```

**关键考虑**:
- 依赖关系必须在使用前建立
- 配置和数据层优先加载
- 主应用逻辑最后初始化

### 3. 模块注册策略

**统一注册模式**:
```javascript
// main.js中的标准注册流程
container.register('configManager', () => new ConfigManager(otaData, airportData));
container.register('channelDetector', () => new ChannelDetector(), ['configManager']);
container.register('fieldMapper', () => new FieldMapper(), ['configManager', 'channelDetector']);
```

### 4. 错误处理策略

**分层错误处理**:
- **UI层**: 用户友好的错误提示
- **业务层**: 业务逻辑验证和降级
- **服务层**: API调用失败重试和回退
- **全局**: 统一的错误收集和日志

## 已知架构问题及改进方案

### 高优先级问题

#### 1. 安全风险
**问题**: 
- API密钥硬编码在`gemini-config.js:42`
- 加密盐值固定在`crypto-utils.js:6`
- 敏感数据存储在明文config.js

**改进方案**:
```javascript
// 环境变量方案
const API_KEY = process.env.GEMINI_API_KEY || '';

// 动态盐值生成
this.salt = crypto.randomBytes(16).toString('hex');

// 敏感数据加密存储
const encryptedData = cryptoUtils.encrypt(sensitiveInfo);
```

#### 2. 性能优化空间
**问题**:
- 重复初始化耗时操作
- 事件监听器清理不完整
- 部分API调用缺乏缓存

**改进方案**:
- 实现模块懒加载机制
- 统一的事件监听器管理
- 扩展缓存覆盖范围

### 中优先级问题

#### 3. 代码重复
**问题**: 
- 配置模块功能重叠
- 工具函数在多模块重复实现

**改进方案**:
- 合并重复配置模块
- 提取公共工具函数库

## 架构演进路径

### 阶段一: 安全加固 (1-2天)
- 移除硬编码敏感信息
- 实现动态密钥管理
- 加强数据加密存储

### 阶段二: 性能优化 (2-3天)
- 扩展缓存系统覆盖
- 优化模块加载机制
- 改进事件管理

### 阶段三: 代码重构 (3-5天)  
- 消除代码重复
- 优化模块依赖关系
- 增强错误处理机制

## 架构原则

### 设计原则
1. **KISS原则**: 保持简单，避免过度设计
2. **单一职责**: 每个模块只做一件事
3. **开放封闭**: 对扩展开放，对修改封闭
4. **依赖倒置**: 高层模块不依赖低层模块

### 编码原则
1. **零依赖**: 禁止引入第三方库
2. **向后兼容**: 新功能不破坏现有接口
3. **性能优先**: 缓存和懒加载是基本要求
4. **安全第一**: 所有外部输入必须验证

### 维护原则
1. **文档同步**: 代码变更必须更新文档
2. **测试覆盖**: 核心功能必须有测试
3. **版本管理**: 重要变更需要版本标记
4. **监控告警**: 关键指标需要监控